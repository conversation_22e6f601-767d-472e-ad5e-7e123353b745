#include  "SysBasic.h"
#include  "RF_BasicFun.h"
//----------------------------------------------------------------------------------------
void RfRxDaemon_01_R(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;
 uint8_t  sta,*ch_tp ;
 if(RfcTimer10[RF_OP_TIMER_R].csr.csbit.q==1)
 {
   U_Area.PaDat_R.PSet.SettingW&=0xdf3f ;//reset the pannel setting byte
   U_Area.PaDat_R.PSta.StaW &=0xf1ff ;//reset the pannel status
   U_Area.PaDat_R.KeyCode =0x0000 ;//reset the key operation status,except for F2nd,F3rd key
   OpCtrl &=~CB_RX_OK_R ;//Turn off the right side RF operation OK flag
  }
 if(RfcTimer10[AIR_TIMER_R].csr.csbit.q==1)
 {
   OpCtrl &=~CB_T_ONLINE_R ;//Turn off the right side linkage OK flag
   U_Area.RSSI_R =-128 ;//RfDs.RSSI_R ;
  }
 if(IsrFlag & IFG_JF01_RX_FINISHED_R)
 {
   RfcTimer[RF_TIMER_R].cv=0 ;//Reset RX attenuation monitor timer
   GPIOC->BRR=0x00000200 ;//PC.9 nCS='0'
   while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
   while(SPI1->SR&0x0001)
   { //empty the receive FIFO
     sta=(u8) SPI1->DR ;//
    } 
   //---------------Begin to read out data in Rx FIFO
   while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready 
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=(~IFG_JF01_RX_FINISHED_R) ;
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
   while((SPI1->SR&0x0001)==0)  ;//Wait the SPI1 to idle
   sta=SPI1->DR ;//Get returned JF01 sta byte
   ch_tp=U_Area.RfRxChBuf0_R ;
   for(tmp=0 ;tmp<RfDs.RxPackageLen ;tmp++)
   {
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
      while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      *ch_tp++=0xaa^SPI1->DR ;//Get the data ;
    }
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
   while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
   *ch_tp++=SPI1->DR ;//Get the data--Append_RSSI_R
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
   while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
   *ch_tp++=SPI1->DR ;//Get the data --Append_CRC_RQI_R
   GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
   IsrFlag |=IFG_RSSI_D_OK_R ;
   __enable_irq();//Enable IRQ
   sta=sta ;
//   U_Area.PaDat_RB0R.PanStaW^=U_Area.PaDat_RB0R.CRC_app ;
//   U_Area.PaDat_RB0R.KeyCode^=U_Area.PaDat_RB0R.CRC_app ;
   if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.RfRxBuf0_R,RfDs.RxPackageLen/2)==0)
   {
     tp1=(uint16_t  *) &U_Area.RfRxBuf0_R ;
     tp2=(uint16_t  *) &U_Area.PaDat_R ;
     for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
     {
       *tp2++=*tp1++ ;//copy valid data to output area
      }
     if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit!=0x0001))
       U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
     // set air linkage valid flag
     RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
     U_Area.DatReq_R=U_Area.PaDat_R.PSet.SettingW ;//Display setting
     OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
     U_Area.RSSI_R=RfDs.RSSI_R ;
     if(RfDs.RSSI_R>=RSSI_LIMIT)
       GFlag |=GF_RSSI_GOOD_R ;
     else
       GFlag &=~GF_RSSI_GOOD_R ;
      //
	 if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	 {
       RfcTimer10[RF_OP_TIMER_R].cv=0 ;
       RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	  }
     RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER_R].cv=0 ;
    }
    else
      RfDs.RF_RxErr_R++ ;//record the CRC error
   }
  if((GPIOC->IDR&0x00000100)&&(RfDs.RxAttenFlag_R!=0x5a5a))//(R_GDO0,DCLK)the CS signal asserted
  {
    if((int8_t)JF01ReadRSSIReg_R()>70)
    {
      JF01WriteReg_R(JF01_FIFOTHR, 0x77);//Set to Rx signal anttenuation to 18dB
      RfDs.RxAttenFlag_R=0x5a5a ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;
      RfcTimer[RF_TIMER_R].cv=0 ;
     }
   }
  if(RfDs.RxAttenFlag_R==0x5a5a)
  {
    if((GPIOC->IDR&0x00000100)==0)//(R_GDO0,DCLK)the CS(carrier sense) signal de-asserted
    {
      RfcTimer[RF_TIMER_R].csr.csword=0x0000 ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      JF01WriteReg_R(JF01_FIFOTHR, 0x47);//Set to default setting,no RF attenuation
      RfDs.RxAttenFlag_R=0x0000 ;
     }
  }
  if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
  {
    RfcTimer[RF_TIMER_R].csr.csword=0x0000 ;
    RfcTimer[RF_TIMER_R].cv=0 ;
    JF01WriteReg_R(JF01_FIFOTHR, 0x47);//Set to default setting,no RF attenuation
    RfDs.RxAttenFlag_R=0x0000 ;
   }
}
//-------------------------------------------------------------------------------
int16_t  Read_RSSI_01_R(void)
{
 uint8_t ctmp ;
 int16_t stmp ;
// ctmp=JF01ReadStaReg_R(JF01_RSSI) ;
 ctmp=U_Area.RfRxChBuf0_R[RfDs.RxPackageLen] ;
 if(ctmp>128)
 {
   stmp=((uint16_t)ctmp-256)>>1 ;
   stmp-=RSSI_OFFSET ;
 }
 else
 {
   stmp=(ctmp>>1)-RSSI_OFFSET ;
 }
 return stmp ;
}

/**********************************************************************************/
__inline void JF01EnterTxMode_R(void)
{
  GPIOC->BSRR=0x00000040 ;//Set PC.6--RFIC_CE(RF_TX_EN in 38409B) to high ='1'
  __disable_irq();//Disable IRQ
  RfDs.RF_State_R=TX_STATUS ;//Set to TX state
  RfDs.RF_RxState_R=RX_NO_INIT_IDLE ;//
  RfDs.RF_TxState_R=TX_READY ;//Set to sending data status
  EXTI->RTSR |=0x00000080 ;//GDO2(R_DIO)generate interrupt on low-to-high transition(sync be sent out)
  EXTI->FTSR &=0x0007ff7f ;//Disable Falling trigger
  EXTI->PR    =0x00000080 ;//clear pending ext interrupt line7# PC.7
  EXTI->IMR  |=0x00000080 ;//Allow PC.7 to generate interrupt
  __enable_irq();//Enable IRQ
}

//---------------------------------------------------------------------------------------
__inline void JF01EnterRxMode_R(void)
{
  GPIOC->BSRR=0x00400000 ;//Reset PC.6--RFIC_CE(RF_TX_EN in 38409B) to low ='0'
  __disable_irq();//Disable IRQ
  RfDs.RF_State_R =RX_STATUS ;//Set to RX state
  RfDs.RF_RxState_R =RX_READY ;//search for a vaild preamble & receiving DP status
  RfDs.RF_TxState_R =TX_IDLE ;//
  EXTI->RTSR |=0x00000080 ;//GDO2(R_DIO)generate interrupt on low-to-high transition(sync have been received)
  EXTI->FTSR &=0x0007ff7f ;//Disable Falling trigger
  EXTI->PR    =0x00000080 ;//clear pending ext interrupt line7# PC.7
  EXTI->IMR  |=0x00000080 ;//Allow PC.7 to generate interrupt
  __enable_irq();//Enable IRQ
}
//---------------------------------------------------------------------------------------
void RfDaemon_01_R(void)
{
 uint8_t i,sta ;
//Process JF01 Initalization operation
 switch (RfDs.JF01Init_R.OpSta)
 {
  case IN_SLEEP_STA :
    if((RfDs.JF01Init_R.OpReq&0xff00)==SET_CODE )
    {
      JF01CmdStrobe_R(JF01_SIDLE);//Put the chip to IDLE status
      RfDs.JF01Init_R.OpSta =RfDs.JF01Init_R.OpReq&0x00ff ;
      RfDs.JF01Init_R.OpReq=0x0000 ;
      JF01WriteReg_R(JF01_TEST2, APP_PD_p->JF01Def.test2); //Various test settings ,lost in sleep status.
      JF01WriteReg_R(JF01_TEST1, APP_PD_p->JF01Def.test1); //Various test settings ,lost in sleep status.
      JF01WriteReg_R(JF01_TEST0, APP_PD_p->JF01Def.test0); //Various test settings ,lost in sleep status.
     }
    break ;
  case IN_RX_STA :
    if((RfDs.JF01Init_R.OpReq&0xff00)==SET_CODE)
    {
      JF01CmdStrobe_R(JF01_SIDLE);//Put the chip to IDLE status
      RfDs.JF01Init_R.OpSta =RfDs.JF01Init_R.OpReq&0x00ff ;
      RfDs.JF01Init_R.OpReq=0x0000 ;
      break ;
     }
//    U_Area.Ultra_BUF_R[0]=JF01ReadStaReg_R(JF01_MARCSTATE) ;
//    if(U_Area.Ultra_BUF_R[0]==0x0001) JF01CmdStrobe_R(JF01_SRX);//
    if(IsrFlag & IFG_JF01_FIFO_ALARM_R)
    {
      GPIOC->BRR=0x00000200 ;//nCS='0'
      while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
      while(SPI1->SR&0x0001)
      { //empty the receive FIFO
        sta=(uint8_t) SPI1->DR ;//
       } 
      while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
      __disable_irq();//Disable all interrupt//Disable IRQ
      IsrFlag &=~IFG_JF01_FIFO_ALARM_R ;//Clear flag
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get returned JF01 sta byte
      for(i=0 ;i<FIFO_THRESHOLD_BYTES-1 ;i++)
      {
        SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
        while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
        *test_p_R=SPI1->DR ;//Get the data ;
        test_p_R++ ;
        RfDs.char_count_R++ ;
        if(RfDs.char_count_R>=80)
        {
          RfDs.char_count_R=0 ;
          test_p_R=(uint8_t *) &U_Area.Ultra_BUF_R ;
         }
       }
       GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
       __enable_irq();//Enable IRQ
       sta=sta ;
      }
    break ;
  case IN_TX_STA :
    if((RfDs.JF01Init_R.OpReq&0xff00)==SET_CODE)
    {
      JF01CmdStrobe_R(JF01_SIDLE);//Put the chip to IDLE status
      RfDs.JF01Init_R.OpSta =RfDs.JF01Init_R.OpReq&0x00ff ;
      RfDs.JF01Init_R.OpReq=0x0000 ;
      break ;
     }
//    U_Area.DS20[0]=JF01ReadStaReg_R(JF01_MARCSTATE) ;
    break ;
  case SET_UP_RX :
   JF01WriteReg_R(JF01_FREQ2,Rx01_Ch_p_R->freq2); // Frequency control word, high byte.
   JF01WriteReg_R(JF01_FREQ1,Rx01_Ch_p_R->freq1); // Frequency control word, middle byte.
   JF01WriteReg_R(JF01_FREQ0,Rx01_Ch_p_R->freq0); // Frequency control word, low byte.
//-----------------------------------------------------------------  
//   JF01WriteReg_R(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
   JF01WriteReg_R(JF01_IOCFG0,0x0e) ;// Set GDO0 to be Asserts when CS(RSSI above threshold)
   JF01WriteReg_R(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
   JF01WriteReg_R(JF01_PKTLEN,RfDs.RxPackageLen); // Set RX Packet length.
   RfDs.JF01Init_R.OpSta=IN_RX_STA ;
   JF01EnterRxMode_R() ;
   JF01CmdStrobe_R(JF01_SRX);//
   break ;
   
  case SET_UP_TX :
   JF01WriteReg_R(JF01_FREQ2,Tx01_Ch_p_R->freq2); // Frequency control word, high byte.
   JF01WriteReg_R(JF01_FREQ1,Tx01_Ch_p_R->freq1); // Frequency control word, middle byte.
   JF01WriteReg_R(JF01_FREQ0,Tx01_Ch_p_R->freq0); // Frequency control word, low byte.
   ShrMachDatTxLoad_01() ;
   JF01WriteReg_R(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
   JF01WriteReg_R(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
   //Prepare and issue the Tx data
   JF01WriteReg_R(JF01_PKTLEN,RfDs.TxPackageLen); // Set TX Packet length.
   JF01WriteFIFO_R((uint8_t *) U_Area.RfTxBuf0,RfDs.TxPackageLen) ;
   RfDs.JF01Init_R.OpSta=IN_TX_STA ;
   JF01EnterTxMode_R() ;
   JF01CmdStrobe_R(JF01_STX);//
   break ;
  case SET_UP_TEST :

   break ;
  case TX_TEST_STA :

   break ;
  case NOT_INIT_STA :
    GPIOC->CRL &=0x0fffffff ;//
    GPIOC->CRL |=0x80000000 ;//Set PC.7(GDO2) to input (float) status
    GPIOC->CRH &=0xfffffff0 ;//
    GPIOC->CRH |=0x00000008 ;//Set PC.8(GDO0) to input (float) status
    ResetJF01_R();//After this chip enter IDLE status
    JF01RegConfig_R(&APP_PD_p->JF01Def) ;
    JF01WritePATable_R(DefPATable,PA_TAB_LEN) ;//I will only use DefPATable[0] normally
    JF01CmdStrobe_R(JF01_SIDLE);//Put the chip to IDLE status
    RfDs.JF01Init_R.OpSta=SET_UP_RX;// SET_UP_TEST;
//    JF01_B_Read_R(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_R[2],47) ;
//    SetupJF01PD_R() ;//Enter sleep status
    break ;
  default : ;
 }
}

//-------------------------------
void  EXTI9_5_ISR_01(void)
{
 if((EXTI->PR&(uint32_t)0x00000080)!=0)//Selected trigger event occurred on the external
 {//interrupt line 7 (PC.7,R_DIO)->GDO2
  EXTI->PR=0x00000080 ; //clear current pending interrupt flag #############&&&&&&&&&&&&&&&
  switch (RfDs.RF_State_R)
  {
    case TX_STATUS:  //TX_STATE
         switch (RfDs.RF_TxState_R)
         {
           case TX_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_SENDING :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_READY :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_RF_TEST :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           default :
                RfDs.RF_TxState_R=TX_IDLE ;
          }
         break;
    case RX_STATUS :  //RX_STATE
         switch (RfDs.RF_RxState_R)
         {
           case RX_NO_INIT_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case RX_DP_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case RX_RECEIVING :
                EXTI->FTSR &=0x0007ff7f ;//disabe falling edge
                EXTI->RTSR |=0x00000080 ;//EXINT 7 (GDO2(DIO)generate interrupt on low-to-high transition(sync have been received)
                RfDs.RF_RxPackage_R++ ;
                IsrFlag |=IFG_JF01_RX_FINISHED_R ;
                RfDs.RF_RxState_R=RX_READY ;
                EXTI->PR=0x00000080 ; //clear current pending interrupt flag #############&&&&&&&&&&&&&&&
                break ;
           case RX_READY :
                EXTI->RTSR |=0x0007ff7f ;//disable rising edge
                EXTI->FTSR |=0x00000080 ;//EXINT 7 (GDO2(DIO)generate interrupt on high-to-low transition
                                         //whole package be received
                RfDs.RF_RxState_R=RX_RECEIVING ;
                IsrFlag |=IFG_JF01_RX_SYNC_FOUND_R ;
                EXTI->PR=0x00000080 ; //clear current pending interrupt flag #############&&&&&&&&&&&&&&&
                break ;
           case RX_RF_TEST :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           default :
                RfDs.RF_RxState_R=RX_NO_INIT_IDLE ;
          }
		 break ;
    case RF_IDLE_STATUS :

         break ;
	default :
	     {
	       RfDs.RF_State_R=RF_IDLE_STATUS ;//=0x0000
           EXTI->IMR  &=0xffffff7f ;//disable (GDO2(DIO),) signal to generate interrupt on this pin
	      }
	}
  }
 if((EXTI->PR&(uint32_t)0x00000100)!=0)//Selected trigger event occurred on the external
 {//interrupt line 8 (PC.8,DCLK-->GDO0)
   EXTI->PR=0x00000100 ; //clear current pending interrupt flag #############&&&&&&&&&&&&&&&
   switch (RfDs.RF_State_R) {
    case TX_STATUS:  //TX_STATE
         switch (RfDs.RF_TxState_R)
         {
           case TX_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_SENDING :
                IsrFlag |=IFG_JF01_FIFO_ALARM_R ;//Set JF01 FIFO alarm flag
                break ;
           case TX_READY :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case TX_RF_TEST :
                IsrFlag |=IFG_JF01_FIFO_ALARM_R ;//Set JF01 FIFO alarm flag
                break ;
           default :
                RfDs.RF_TxState_R=TX_IDLE ;
          }
         break;
    case RX_STATUS :  //RX_STATE
         switch (RfDs.RF_RxState_R)
         {
           case RX_NO_INIT_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case RX_DP_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case RX_RECEIVING :
                IsrFlag |=IFG_JF01_FIFO_ALARM_R ;//Set JF01 FIFO alarm flag
                break ;
           case RX_READY :
                IsrFlag |=IFG_JF01_INT_ERR_R ;
                break ;
           case RX_RF_TEST :
                IsrFlag |=IFG_JF01_FIFO_ALARM_R ;//Set JF01 FIFO alarm flag
                break ;
           default :
                RfDs.RF_RxState_R=RX_NO_INIT_IDLE ;
          }
		 break ;
    case RF_IDLE_STATUS :

         break ;
	default :
	     {
	       RfDs.RF_State_R=RF_IDLE_STATUS ;//=0x0000
           EXTI->IMR  &=0xfffffeff ;//disable (GDO0(L_DCLK),) signal to generate interrupt on this pin
	      }
   }    
 }
}

//-------------------------------------------------
void JF01RegConfig_R(JF01Registers  *rfConfig)
{
  JF01WriteReg_R(JF01_FSCTRL1,  rfConfig->fsctrl1);    // Frequency synthesizer control.
  JF01WriteReg_R(JF01_FSCTRL0,  rfConfig->fsctrl0);    // Frequency synthesizer control.
  JF01WriteReg_R(JF01_FREQ2,    Rx01_Ch_p_R->freq2);      // Frequency control word, high byte.
  JF01WriteReg_R(JF01_FREQ1,    Rx01_Ch_p_R->freq1);      // Frequency control word, middle byte.
  JF01WriteReg_R(JF01_FREQ0,    Rx01_Ch_p_R->freq0);      // Frequency control word, low byte.
  JF01WriteReg_R(JF01_MDMCFG4,  rfConfig->mdmcfg4);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG3,  rfConfig->mdmcfg3);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG2,  rfConfig->mdmcfg2);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG1,  rfConfig->mdmcfg1);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG0,  rfConfig->mdmcfg0);    // Modem configuration.
  JF01WriteReg_R(JF01_CHANNR,   rfConfig->channr);     // Channel number.
  JF01WriteReg_R(JF01_DEVIATN,  rfConfig->deviatn);    // Modem deviation setting (when FSK modulation is enabled).
  JF01WriteReg_R(JF01_FREND1,   rfConfig->frend1);     // Front end RX configuration.
  JF01WriteReg_R(JF01_FREND0,   rfConfig->frend0);     // Front end RX configuration.
  JF01WriteReg_R(JF01_MCSM0,    rfConfig->mcsm0);      // Main Radio Control State Machine configuration.
  JF01WriteReg_R(JF01_FOCCFG,   rfConfig->foccfg);     // Frequency Offset Compensation Configuration.
  JF01WriteReg_R(JF01_BSCFG,    rfConfig->bscfg);      // Bit synchronization Configuration.
  JF01WriteReg_R(JF01_AGCCTRL2, rfConfig->agcctrl2);   // AGC control.
  JF01WriteReg_R(JF01_AGCCTRL1, rfConfig->agcctrl1);   // AGC control.
  JF01WriteReg_R(JF01_AGCCTRL0, rfConfig->agcctrl0);   // AGC control.
  JF01WriteReg_R(JF01_FSCAL3,   rfConfig->fscal3);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL2,   rfConfig->fscal2);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL1,   rfConfig->fscal1);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL0,   rfConfig->fscal0);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSTEST,   rfConfig->fstest);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FIFOTHR,  rfConfig->fifothr);    // Set TX FIFO threshold:Bytes in TX buffer=61,Bytes in RX FIFO =4
  JF01WriteReg_R(JF01_IOCFG2,   rfConfig->iocfg2);     // GDO2 output pin configuration.
  JF01WriteReg_R(JF01_IOCFG0,   rfConfig->iocfg0);     // GDO0 output pin configuration.
  JF01WriteReg_R(JF01_MCSM1,    rfConfig->mcsm1) ;     // CCA mode=Always,RXOFF_MODE=IDLE,TXOFF_MODE=Switch to RX status
  JF01WriteReg_R(JF01_SYNC1,    rfConfig->sync1) ;     //0xcc
  JF01WriteReg_R(JF01_SYNC0,    rfConfig->sync0) ;     //0x33
  JF01WriteReg_R(JF01_PKTCTRL1, rfConfig->pktctrl1);   // Packet automation control.
  JF01WriteReg_R(JF01_PKTCTRL0, rfConfig->pktctrl0);   // Packet automation control.
//JF01WriteReg_R(JF01_ADDR, rfConfig->addr);  // Device address.
}
//---------------
uint8_t ReadJF01FSCal_R(uint8_t *Data_p)
{//read back he Frequency synthesizer calibration result
  return(JF01_B_Read_R(JF01_FSCAL3,Data_p,3)) ;
}
//---------------------------------------------
void ResetJF01_R(void)
{
  //To reset the JF01
  GPIOA->BSRR=0x00800020 ;//Set PA.7 to '0',PA.5 to'1'
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_GPIO_CONFIG ;//Set pin PA5(SCLK),6(MISO),7(MOSI) to GPIO mode
  WaitSomeTime(TIME_20uS_V) ;
  GPIOC->BRR=0x00000200 ;//Set JF01 nCS Pin to low
  WaitSomeTime(TIME_40uS_V) ;
  GPIOC->BSRR=0x00000200  ;//Left channel JF01 nCS Pin to high,/nCS=1,MISO=0,SCLK=1
  WaitSomeTime(TIME_40uS_V) ;
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_SPI_CONFIG ;//Set pin PB13(SCLK),14(MISO),15(MOSI) to SPI mode
  GPIOC->BRR=0x00000200 ;//Set nCS Pin to '0'
  while((GPIOA->IDR&0x00000040)!=0)   ;//Wait for JF01 SO to low(ready for SPI operation)
  SPI1->DR=JF01_SRES ;//Issue a command strobe SRES -reset the chip
  while((SPI1->SR&0x0001)==0);//Wait for the data be output
  while((GPIOA->IDR&0x00000040)!=0) ;//Wait for JF01 SO to low(ready for SPI operation)
  GPIOC->BSRR=0x00000200 ;//Set nCS Pin to '1'
  RFChipOscSta=RFIC_OSC_READY ;
}

//-------------------------------------------------

void SetupJF01PD_R(void)
{
//  GPIOC->BSRR=0x00000200 ;//Set nRx_Tx_EN='1'
  __disable_irq();//Disable IRQ
  EXTI->PR  =0x0000000c ;//clear current pending interrupt flag #############&&&&&&&&&&&&&&&
  EXTI->IMR &=0xfffffff3 ;//disable (GDO2(DIO),GDO0(DCLK)) signal to generate interrupt
  RfDs.RF_State_R   = RF_IDLE_STATUS ;
  RfDs.RF_TxState_R = TX_IDLE ;//
  RfDs.RF_RxState_R = RX_NO_INIT_IDLE ;//
  JF01CmdStrobe_R(JF01_SPWD);//Set JF01 to power down(sleep mode)
  RfDs.JF01Init_R.OpSta =IN_SLEEP_STA ;//Enter sleep status
  __enable_irq();//Enable IRQ
  RFChipOscSta=RFIC_OSC_UNKNOW ;//The RFChipOscSta must stay in RFIC_OSC_READY status for right side operation,
                                //0x18 register MCSM0 setting,XOSC_FORCE_ON=1
}
//-------------------------------------------------

uint8_t JF01_B_Read_R(uint8_t Add, uint8_t *Data_p,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt ,enable __enable_irq()
  SPI1->DR=Add+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=0x00 ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  GPIOC->BSRR=0x00000200  ;//nCS='1'
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadReg_R(uint8_t Add, uint8_t *Data_p)
{
  uint8_t sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
  } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_READ_SINGLE ;//Issue the address byte
  while((SPI1->SR&0x0001)==0) ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  SPI1->DR=0x00 ;//Dummy data
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  *Data_p=SPI1->DR ;//Get the data
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t  JF01ReadRSSIReg_R(void)
{
  uint8_t sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_RSSI+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  SPI1->DR=0x00 ;//Issue the dummy byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status register content
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadStaReg_R(uint8_t Add)
{
  uint8_t sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
  } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  SPI1->DR=0x00 ;//Issue the dummy byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status register content
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01GetRxSta_R(void)
{
  uint8_t pre_sta,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    pre_sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_SNOP+JF01_READ_SINGLE ;////Issue Nop operation cmd
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  do{
      pre_sta=sta ;
      SPI1->DR=JF01_SNOP+JF01_READ_SINGLE ;//Issue Nop operation cmd
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get the JF01 status byte
     } while(pre_sta!=sta) ;
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01GetTxSta_R(void)
{
  uint8_t pre_sta,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    pre_sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_SNOP ;////Issue Nop operation cmd
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  do{
      pre_sta=sta ;
      SPI1->DR=JF01_SNOP ;//Issue Nop operation cmd
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get the JF01 status byte
     } while(pre_sta!=sta) ;
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01_B_Write_R(uint8_t Add, uint8_t *Data_p,uint8_t Len)
{
  uint8_t i,sta,tmp ;
  tmp=0 ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Issue the write data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    tmp=SPI1->DR ;//Dummy read
   }
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  tmp=tmp ;
  return sta ;
}

void JF01WriteReg_R(uint8_t Add, uint8_t Data)
{
  uint8_t sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  SPI1->DR=Data ;//Issue the write data
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  sta=sta ;
}

uint8_t JF01CmdStrobe_R(uint8_t cmd)
{
  uint8_t sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=cmd ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadFIFO_R(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01WriteFIFO_R(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_TXFIFO+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//dummy read
   }
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadPATable_R(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_PATABLE+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=JF01_PATABLE+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01WritePATable_R(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta,tmp ;
  tmp=0;
  GPIOC->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_PATABLE+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    tmp=SPI1->DR ;//dummy read
   }
  GPIOC->BSRR=0x00000200 ;  // Set /nCS to High 
  __enable_irq();
  tmp=tmp ;
  return sta ;
}
//--------------------EOF-------------------------------

