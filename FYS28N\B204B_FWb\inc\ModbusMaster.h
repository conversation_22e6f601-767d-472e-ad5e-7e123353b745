#ifndef __MD_MASTER_FUN_H
#define __MD_MASTER_FUN_H

#include  "stm32f10x.h"
#include  "ThisDevice.h"
#include  "Timer.h"
#include  "UartFun_A.h"
#include  "ModbusRTU_A.h"



#define  M_WR_ADD_L    28  //Master intended write register address--40028
#define  M_WR_LEN_L    4   //Master intended write register number(length)
#define  M_RD_ADD_L    16  //Master intended read register address--40017
#define  M_RD_LEN_L    4   //Master intended read register number(length)

#define  M_WR_ADD_R    32  //Master intended write register address--40033
#define  M_WR_LEN_R    4   //Master intended write register number(length)
#define  M_RD_ADD_R    20  //Master intended read register address--40021
#define  M_RD_LEN_R    4   //Master intended read register number(length)

#define  HOST_ID        5 //MODBUS monitor computer address

extern  timer_t U_Timer[U_TIM_NUM]  ;

extern  UARTStruct  UA, UC  ;//

extern  ModStruct  ModA, ModC ;

extern  volatile   uint32_t   OpCtrl ;

extern  volatile   uint32_t   GFlag  ;

extern  SysAreaStruct    S_Area ;//Extern defined an object for system data --in file ".h"

extern  UserAreaStruct   U_Area ;//Extern defined an object for user data

void  ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum) ;

void  ActiveReceiver(void) ;

void  AccessOPS(void) ;//

#endif   //__MD_MASTER_FUN_H
//--------------------------------


