#include "MA_Certify_Fun.h"
#include "ModbusMaster.h"
#include "UartFun_A.h"

void MA_SendTo204B(void)
{
 U_Area.KeyComb =U_Area.PaDat_L.KeyCode ;
 U_Area.KeyComb|=U_Area.PaDat_R.KeyCode ;
 if(((OpCtrl&(CB_RX_OK_L+CB_RX_OK_R))!=0)||(U_Area.KeyComb!=0))
 {
  if(UA.tcs==U_TX_IDLE)
  {
    //U_Area.KeyComb --set in FYS30--Proc10mS_S30 function
	Tx_UA((volatile uint8_t *)&U_Area.KeyComb,2);
   }
  }
}
//--------

