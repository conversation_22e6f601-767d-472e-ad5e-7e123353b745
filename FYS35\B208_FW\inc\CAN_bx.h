/*******CAN_bx.h for CAN fuunction ********/
#ifndef __CAN_BX_FUN_H
#define __CAN_BX_FUN_H
#include "stm32f10x.h"
/**************************************************************************/

/**********Function related to use CAN bus (use bxCAN module) ************/

//According to CAN 2.0A Specification ,for STD ID most 7 bit of ID must not be all recessive
//--------------------

#define  PDO_TX_TIMER         0 //used as CAN TPDO TX(refresh) timer

#define  SYNC_O_TIMER         1 //used as CAN SYNC_O TX(refresh) timer

#define  RPDO_TX_TIMER        2 //used as CAN RPDO TX(refresh) timer  --for system debug test

#define  CAN_TIMER10_NUM      2 //CanTimer10

#define  TPDO_M_TIMER         0 //used to monitor CAN TPDO object receiving

//-------

//-------------------- CANOpen related define
#define COB_ID_COM_TX           0x00000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen node ID mask bits in CAN_TIxR
                                            // 0 0 0 0 '0 0 0 0 '0 0 0 0 '0 0 0 0 --COB_ID used Standard Identifier
#define COB_ID_COM_RX           0x00000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen node ID bits in CAN_RIxR
                                            // 0 0 0 0 '0 0 0 0 '0 0 0 0 '0 0 0 0 --COB_ID used Standard Identifier
#define COB_STD_ID_MASK         0x00000004  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen node ID mask bit in CAN_RIxR
                                            // 0 0 0 0 '0 0 0 0 '0 0 0 0 '0 1 0 0 --COB_ID only used Standard Identifier
#define COB_NODE_ID_MASK        0x0fe00000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen node ID mask bit in CAN_RIxR
                                            // 0 0 0 0 '1 1 1 1 '1 1 1 0 '0 0 0 0 --COB_ID only used Standard Identifier
#define COB_FUNCTION_ID_MASK    0xf0000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen Function ID mask bit in CAN_RIxR
                                            // 1 1 1 1 '0 0 0 0 '0 0 0 0 '0 0 0 0 --COB_ID only used Standard Identifier
#define COB_RXDO_ID_MASK        0x10000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen node ID mask bit in CAN_RIxR
                                            // 0 0 0 1 '0 0 0 0 '0 0 0 0 '0 0 0 0 --COB_ID only used Standard Identifier

#define COB_NMT_FUN_ID          0x00000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 0 0 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_SYNC_FUN_ID         0x10000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 0 0 1 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_TIME_STAMP_FUN_ID   0x20000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 0 1 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_EMERGENCY_FUN_ID    0x10000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 0 0 1 '0 0 0 0 '0 0 0 0 '0	0 0	0

#define COB_TPDO1_FUN_ID        0x30000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 0 1 1 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_RPDO1_FUN_ID        0x40000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 1 0 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_TPDO2_FUN_ID        0x50000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 1 0 1 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_RPDO2_FUN_ID        0x60000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 1 1 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_TPDO3_FUN_ID        0x70000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 0 1 1 1 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_RPDO3_FUN_ID        0x80000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 0 0 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_TPDO4_FUN_ID        0x90000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 0 0 1 '0 0 0 0 '0 0 0 0 '0 0 0	0
#define COB_RPDO4_FUN_ID        0xa0000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 0 1 0 '0 0 0 0 '0 0 0 0 '0	0 0	0

#define COB_TSDO_FUN_ID         0xb0000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 0 1 1 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_RSDO_FUN_ID         0xc0000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 1 0 0 '0 0 0 0 '0 0 0 0 '0	0 0	0
#define COB_NMT_ERR_FUN_ID      0xe0000000  // F E D C 'B A 9 8 '7 6 5 4 '3 2 1 0 --CANopen NMT Function ID
                                            // 1 1 1 0 '0 0 0 0 '0 0 0 0 '0	0 0	0

//--------------------

typedef struct TagCANObjStruct
{
 uint16_t *tp ;
 uint32_t tmp ;
 uint32_t tmp2 ;
} CANObjStruct ;
/**************************************************************************************/

typedef struct TagCANOpenObjStruct
{
 uint16_t COB_ID ;
 uint16_t RxTxInterval ;//Rx time in 0.1mS unit,Tx time in 1mS unit
 uint16_t Data[4] ;
} CANOpenObjStruct ;

/**************************************************************************************/
void  CanTimerFun(void) ;//This function must be called in system tick ISR for proper CAN_bx(RTM)

void  CanTimer10Fun(void) ;//This function must be called ervery 10 ms  for proper CAN device monitor

void  InitCanOpen(void) ;//init CAN interface for CANOpen

void  CANErrMonitor(void) ;//a routine used to monitor CAN module operation

void  CANErrMonitor_ISR(void) ;//a routine used to monitor CAN module operation

void  CAN_RX0_ISR_G(void) ;//CANOpen mode ISR for RTM RX using FIFO 0 in STM32 for general use

void  CAN_RX1_ISR_G(void) ;//CANOpen mode ISR for RTM RX using FIFO 1 in STM32 for general use

void  CAN_Proc_G(void) ;//Basic CAN bus RTM TX and RX data process function for general use

void  CAN_RX0_ISR_JX(void) ;//CANOpen mode ISR for RTM RX using FIFO 0 in STM32 for JXMJ

void  CAN_RX1_ISR_JX(void) ;//CANOpen mode ISR for RTM RX using FIFO 1 in STM32 for JXMJ

void  CAN_Proc_JX(void) ;//Basic CAN bus RTM TX and RX function for JXMJ

void  CANOpenHostSimProc(void) ; 

#endif   //__CAN_BX_FUN_H

