# FYS28N 协议转换程序流程分析

## 系统架构概述

### 数据流向
```
FYS35(485型号)接收机 → FYS 28N(485接口) → FYS 28N(CAN总线) → PLC(CAN总线2.0)
     ↑                                                                    ↓
     ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

### 核心功能定位
- **FYS28N**: 作为协议转换网关，实现Modbus RTU从机 ↔ CANOpen主机的双向转换
- **主要任务**: 接收FYS35的Modbus数据，转换为CANOpen PDO发送给PLC，同时接收PLC的CANOpen数据转发给FYS35

## 主要数据结构

### 1. PDO数据对象结构
```c
// TPDO发送数据结构 (FYS28N → PLC)
typedef struct TagTPDOStruct {
    uint16_t  KeyCode_R;    // 右侧按键状态
    uint16_t  KeyCode_L;    // 左侧按键状态  
    uint8_t   PanSta_R;     // 右侧面板状态
    uint8_t   PanSta_L;     // 左侧面板状态
    int8_t    RSSI_R;       // 右侧信号强度
    int8_t    RSSI_L;       // 左侧信号强度
} TPDOStruct;

// RPDO接收数据结构 (PLC → FYS28N)
typedef struct TagRPDOStruct {
    uint16_t  rdata_1;      // 第1个16位数据
    uint16_t  rdata_2;      // 第2个16位数据
    uint16_t  rdata_3;      // 第3个16位数据
    uint16_t  rdata_4;      // 第4个16位数据
} RPDOStruct;
```

### 2. Modbus通信结构
```c
typedef struct TagModStruct {
    uint8_t   Add;          // 设备地址
    uint8_t   Fun;          // 功能码
    uint16_t  RegAdd;       // 寄存器地址
    uint16_t  DatNum;       // 数据数量
    uint8_t   *SDp;         // 发送数据指针
    uint8_t   *RSDp;        // 接收数据指针
    uint8_t   Sta;          // 状态字
    uint16_t  Err;          // 错误标志
} ModStruct;
```

## 核心程序流程

### 1. 主循环处理 (__JF01_Main)
```c
void __JF01_Main(void) {
    while(1) {
        // RF数据处理
        RfDaemon_01_L();
        RfDaemon_01_R();
        RfRxDaemon_01_L();
        RfRxDaemon_01_R();
        
        // Modbus协议处理
        ModProcA(&UA, &ModA);  // 485接口A - 与FYS35通信
        ModProcA(&UC, &ModC);  // 485接口C - 与PLC通信(备用)
        
        // CAN总线处理
        if(APP_PB.CAN_Mode != 0) {
            (*CanFun_fp)();  // CAN功能处理
        }
        
        // 定时任务处理
        TimerProc();
    }
}
```

### 2. 定时处理任务

#### 10ms定时任务 (Proc10mS_JF01)
```c
void Proc10mS_JF01(void) {
    U_Area.Counter10ms++;
    
    if(S_Area.EStop_Timer)
        S_Area.EStop_Timer--;
        
    if((APP_PB.CAN_Mode != 0) && (APP_PB.CAN_Mode < 0x0100)) {
        Dat2HostProc_01();      // 数据转换处理
        Canopen2DisplayBuf();   // CANOpen数据显示缓冲
    }
}
```

#### 100ms定时任务 (Proc100mS_JF01)
```c
void Proc100mS_JF01(void) {
    CANErrMonitor();  // CAN总线错误监控
}
```

### 3. Modbus从机处理流程

#### Modbus接收处理 (ModProcA)
```c
void ModProcA(UARTStruct *UX, ModStruct *ModX) {
    // 1. 检查接收状态
    if(((*ModX).Sta & 0x88) != 0) {
        // 2. 处理发送请求
        if((*ModX).Opc < 0x80 && (*ModX).Sta == 0x08) {
            // 从机模式响应
            switch((*ModX).Fun) {
                case 0x03:  // 读保持寄存器
                case 0x04:  // 读输入寄存器
                    // 读取数据处理
                    break;
                case 0x06:  // 写单个寄存器
                case 0x10:  // 写多个寄存器
                    // 写入数据处理
                    break;
            }
        }
    }
    
    // 3. 接收数据处理
    if((*ModX).Sta == 0x20) {
        // 解析接收到的Modbus帧
        // 更新本地数据缓冲区
    }
}
```

### 4. CANOpen主机处理流程

#### CANOpen初始化 (InitCanOpen)
```c
void InitCanOpen(void) {
    // 1. 设置节点ID
    U_Area.CANOpenID = (uint16_t)APP_PB.CANOpenID;
    
    // 2. 设置定时参数
    U_Area.SYNC_Interval = APP_PB.SYNC_Interval;
    U_Area.PDO_Interval = APP_PB.PDO_Interval;
    
    // 3. 配置CAN总线速率 (125k/250k/500k)
    if(APP_PB.CAN_Speed == 500) {
        CAN1->BTR = 0x001c0003;  // 500kbps配置
    } else if(APP_PB.CAN_Speed == 250) {
        CAN1->BTR = 0x001c0007;  // 250kbps配置  
    } else {
        CAN1->BTR = 0x001c000f;  // 125kbps配置
    }
    
    // 4. 启动PDO定时器
    CanTimer[PDO_TX_TIMER].pv = U_Area.PDO_Interval;
    CanTimer[SYNC_O_TIMER].pv = U_Area.SYNC_Interval;
}
```

#### TPDO发送处理 (CANOpenTxProc_JX)
```c
void CANOpenTxProc_JX(void) {
    if(CanTimer[PDO_TX_TIMER].csr.csbit.q == 1) {
        // 1. 构造CAN ID
        uint32_t tmp32 = U_Area.CANOpenID;
        tmp32 <<= 21;
        tmp32 |= COB_TPDO1_FUN_ID;  // TPDO1功能码
        
        // 2. 设置数据
        uint16_t *tp = (uint16_t *)&U_Area.RF_PDODat[0];
        CAN1->sTxMailBox[TransmitMailbox].TDLR = 
            (uint32_t)*(tp+1) << 16 | (uint32_t)*tp;
        CAN1->sTxMailBox[TransmitMailbox].TDHR = 
            (uint32_t)*(tp+3) << 16 | (uint32_t)*(tp+2);
            
        // 3. 发送PDO
        CAN1->sTxMailBox[TransmitMailbox].TIR |= 0x00000001;
        
        // 4. 重启定时器
        CanTimer[PDO_TX_TIMER].cv = 0;
    }
}
```

#### RPDO接收处理 (CAN_RX0_ISR_JX)
```c
void CAN_RX0_ISR_JX(void) {
    // 1. 解析CAN ID
    uint32_t tmp32 = (CAN1->sFIFOMailBox[0].RIR) >> 21;
    uint16_t node_id = (uint16_t)(tmp32 & 0x0000007f);
    
    // 2. 检查节点ID匹配
    RPDOStruct *pdo_p;
    if(node_id == APP_PB.CANOpenR_ID_1) {
        pdo_p = &U_Area.RPDO_ID1[0];
    } else if(node_id == APP_PB.CANOpenR_ID_2) {
        pdo_p = &U_Area.RPDO_ID2[0];
    }
    
    // 3. 复制接收数据
    (*pdo_p).rdata_1 = (uint16_t)(CAN1->sFIFOMailBox[0].RDLR);
    (*pdo_p).rdata_2 = (uint16_t)(CAN1->sFIFOMailBox[0].RDLR >> 16);
    (*pdo_p).rdata_3 = (uint16_t)(CAN1->sFIFOMailBox[0].RDHR);
    (*pdo_p).rdata_4 = (uint16_t)(CAN1->sFIFOMailBox[0].RDHR >> 16);
    
    // 4. 设置接收完成标志
    U_Area.WiredComSta |= 0x0010;
}
```

## 协议转换关键点

### 1. 数据映射关系
- **Modbus寄存器** ↔ **CANOpen PDO对象**
- **40001-40008**: RF控制数据 → TPDO1
- **40017-40036**: 状态反馈数据 ← RPDO1/RPDO2

### 2. 定时同步机制
- **PDO发送周期**: 25ms (可配置)
- **SYNC同步周期**: 100ms (可配置)
- **Modbus轮询周期**: 根据主机设定

### 3. 错误处理机制
- **CAN总线错误监控**: CANErrMonitor()
- **Modbus通信超时**: 错误计数器记录
- **数据完整性检查**: CRC校验

## 配置参数

### CANOpen相关参数
- `CANOpenID`: 本节点ID (1-127)
- `CANOpenR_ID_1/2`: 接收节点ID
- `CAN_Speed`: 总线速率 (125/250/500 kbps)
- `PDO_Interval`: PDO发送间隔 (4-2000ms)
- `SYNC_Interval`: SYNC间隔 (≥5ms)

### Modbus相关参数  
- `HOST_ID`: 主机地址 (通常为0x05)
- 波特率、数据位、停止位等串口参数

## 详细数据转换流程

### 1. FYS35 → FYS28N (Modbus RTU)

#### 数据接收流程
```c
// FYS35作为Modbus主机，FYS28N作为从机
void ModbusSlaveProcess(void) {
    // 1. 接收FYS35的写命令 (功能码0x10)
    if(ModA.Fun == ECS_MWRITE) {  // 0x10
        // 写入地址40001开始的8个寄存器
        if(ModA.RegAdd == 0x0000 && ModA.DatNum == 8) {
            // 复制RF控制数据到本地缓冲区
            memcpy(&U_Area.PaDat_L, ModA.RSDp, 16);

            // 更新TPDO数据结构
            U_Area.RF_TPDO.KeyCode_L = U_Area.PaDat_L.KeyCode;
            U_Area.RF_TPDO.KeyCode_R = U_Area.PaDat_R.KeyCode;
            // ... 其他状态数据
        }
    }

    // 2. 响应FYS35的读命令 (功能码0x03)
    if(ModA.Fun == ECS_RDHR) {  // 0x03
        // 读取地址40017开始的20个寄存器
        if(ModA.RegAdd == 0x0010 && ModA.DatNum == 20) {
            // 从RPDO数据更新显示缓冲区
            UpdateDisplayBuffer();
            // 返回状态数据给FYS35
            ModA.SDp = (uint8_t*)&U_Area.DisplayDatBuf;
        }
    }
}
```

### 2. FYS28N → PLC (CANOpen)

#### TPDO发送流程
```c
void Dat2HostProc_01(void) {
    // 1. 组装RF数据到PDO
    U_Area.RF_TPDO.KeyCode_L = U_Area.PaDat_L.KeyCode;
    U_Area.RF_TPDO.KeyCode_R = U_Area.PaDat_R.KeyCode;
    U_Area.RF_TPDO.PanSta_L = 0;
    U_Area.RF_TPDO.PanSta_R = 0;

    // 2. 设置状态标志
    if(U_Area.RSSI_L >= (RSSI_LIMIT + 5))
        U_Area.RF_TPDO.PanSta_L |= 0x40;  // RSSI良好
    if(OpCtrl & CB_T_ONLINE_L)
        U_Area.RF_TPDO.PanSta_L |= 0x10;  // 发射机在线

    // 3. 更新RSSI值
    U_Area.RF_TPDO.RSSI_L = (int8_t)U_Area.RSSI_L;
    U_Area.RF_TPDO.RSSI_R = (int8_t)U_Area.RSSI_R;

    // 4. 计算组合按键
    U_Area.KeyComb = U_Area.RF_TPDO.KeyCode_L | U_Area.RF_TPDO.KeyCode_R;
}
```

### 3. PLC → FYS28N (CANOpen)

#### RPDO接收处理
```c
void ProcessRPDOData(void) {
    // 1. 检查RPDO接收标志
    if(U_Area.WiredComSta & 0x0010) {
        // 2. 处理来自PLC的控制命令
        ProcessPLCCommands(&U_Area.RPDO_ID1[0]);
        ProcessPLCCommands(&U_Area.RPDO_ID2[0]);

        // 3. 更新本地状态
        UpdateLocalStatus();

        // 4. 清除接收标志
        U_Area.WiredComSta &= 0xFFEF;
    }
}

void ProcessPLCCommands(RPDOStruct *rpdo) {
    // 解析PLC发送的控制命令
    uint16_t cmd_type = rpdo->rdata_1;
    uint16_t cmd_data = rpdo->rdata_2;

    switch(cmd_type) {
        case 0x0001:  // 急停命令
            HandleEmergencyStop(cmd_data);
            break;
        case 0x0002:  // 运行控制
            HandleRunControl(cmd_data);
            break;
        // ... 其他命令处理
    }
}
```

### 4. FYS28N → FYS35 (Modbus RTU)

#### 状态数据回传
```c
void UpdateDisplayBuffer(void) {
    // 1. 从RPDO数据更新显示缓冲区
    U_Area.DisplayDatBuf[0] = U_Area.RPDO_ID1[0].rdata_1;
    U_Area.DisplayDatBuf[1] = U_Area.RPDO_ID1[0].rdata_2;
    U_Area.DisplayDatBuf[2] = U_Area.RPDO_ID1[0].rdata_3;
    U_Area.DisplayDatBuf[3] = U_Area.RPDO_ID1[0].rdata_4;

    // 2. 添加本地状态信息
    U_Area.DisplayDatBuf[4] = U_Area.WiredComSta;  // 通信状态
    U_Area.DisplayDatBuf[5] = U_Area.CAN_Sta.TxCounter;  // CAN发送计数
    U_Area.DisplayDatBuf[6] = U_Area.CAN_Sta.RxCounter;  // CAN接收计数

    // 3. 系统诊断信息
    U_Area.DisplayDatBuf[7] = U_Area.Err;  // 错误代码
    U_Area.DisplayDatBuf[8] = (uint16_t)(U_Area.Counter10ms & 0xFFFF);  // 运行计数
}
```

## 关键时序控制

### 1. 定时器配置
```c
// 系统定时器初始化
void InitTimers(void) {
    // PDO发送定时器 (25ms)
    CanTimer[PDO_TX_TIMER].pv = U_Area.PDO_Interval;
    CanTimer[PDO_TX_TIMER].cv = 0;
    CanTimer[PDO_TX_TIMER].csr.csword = 0x4000;

    // SYNC同步定时器 (100ms)
    CanTimer[SYNC_O_TIMER].pv = U_Area.SYNC_Interval;
    CanTimer[SYNC_O_TIMER].cv = 0;
    CanTimer[SYNC_O_TIMER].csr.csword = 0x4000;

    // RPDO监控定时器 (100ms)
    CanTimer10[TPDO_M_TIMER].pv = 10;  // 100ms
    CanTimer10[TPDO_M_TIMER].cv = 0;
    CanTimer10[TPDO_M_TIMER].csr.csword = 0x4000;
}
```

### 2. 通信状态监控
```c
void CANErrMonitor(void) {
    // 1. 检查CAN总线状态
    if(CAN1->ESR & 0x00000007) {  // 错误状态
        U_Area.CAN_Sta.ErrCounter++;
        // 错误恢复处理
    }

    // 2. 检查RPDO接收超时
    if(CanTimer10[TPDO_M_TIMER].csr.csbit.q == 1) {
        U_Area.WiredComSta &= 0xFFEF;  // 清除PDO接收标志
        U_Area.CAN_Sta.TimeoutCounter++;
    }

    // 3. 更新通信状态
    if(U_Area.WiredComSta & 0x0010) {
        U_Area.CAN_Sta.ComStatus = 1;  // 通信正常
    } else {
        U_Area.CAN_Sta.ComStatus = 0;  // 通信异常
    }
}
```

## 错误处理与诊断

### 1. Modbus通信错误
```c
void ModbusErrorHandler(ModStruct *ModX) {
    if((*ModX).Err & 0x8000) {  // 无响应错误
        U_Area.U1ModOp.ot_err++;
        // 重试机制
    }

    if((*ModX).Err & 0x0100) {  // CRC错误
        U_Area.U1ModOp.crc_err++;
        // 数据重传
    }
}
```

### 2. CANOpen通信错误
```c
void CANOpenErrorHandler(void) {
    // 总线关闭错误
    if(CAN1->ESR & 0x00000004) {
        // 自动恢复
        CAN1->MCR |= 0x00000040;  // 自动总线关闭管理
    }

    // 发送错误
    if(CAN1->TSR & 0x00000008) {
        U_Area.CAN_Sta.TxErrCounter++;
        // 清除错误标志
        CAN1->TSR |= 0x00000008;
    }
}
```

## 配置与调试

### 1. 参数配置接口
```c
// 通过Modbus配置CANOpen参数
void ConfigCANOpenParams(uint16_t addr, uint16_t value) {
    switch(addr) {
        case 0x0125:  // CANOpen节点ID
            APP_PB.CANOpenID = (uint8_t)(value & 0x7F);
            break;
        case 0x0126:  // 接收节点ID
            APP_PB.CANOpenR_ID_1 = (uint8_t)(value & 0xFF);
            APP_PB.CANOpenR_ID_2 = (uint8_t)(value >> 8);
            break;
        case 0x0127:  // SYNC间隔
            APP_PB.SYNC_Interval = value;
            break;
        case 0x0128:  // PDO间隔
            APP_PB.PDO_Interval = value;
            break;
    }
}
```

### 2. 调试信息输出
```c
void DebugOutput(void) {
    // 通信统计信息
    U_Area.DisplayDatBuf[10] = U_Area.U1ModOp.Packages;     // Modbus包计数
    U_Area.DisplayDatBuf[11] = U_Area.CAN_Sta.TxCounter;    // CAN发送计数
    U_Area.DisplayDatBuf[12] = U_Area.CAN_Sta.RxCounter;    // CAN接收计数
    U_Area.DisplayDatBuf[13] = U_Area.U1ModOp.crc_err;      // CRC错误计数
    U_Area.DisplayDatBuf[14] = U_Area.U1ModOp.ot_err;       // 超时错误计数
}
```

## 总结

FYS28N通过双协议栈设计，实现了Modbus RTU从机与CANOpen主机的无缝转换，为工业现场的异构网络互联提供了可靠的解决方案。关键特性包括：

1. **实时数据转换**: 25ms周期的PDO数据交换
2. **双向通信**: 支持控制数据下发和状态数据上传
3. **错误恢复**: 完善的错误检测和自动恢复机制
4. **灵活配置**: 支持运行时参数配置和调整
5. **诊断功能**: 丰富的通信状态和错误统计信息
