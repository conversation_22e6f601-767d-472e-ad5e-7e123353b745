//----------------------------------------------------
#include  "SysBasic.h"
#include  "UartFun_A.h"
#include  "RF_BasicFun.h"
#include  "RF_Test_021.h"

extern  UARTStruct   UA ;//
extern  ModStruct  ModA ;

extern  volatile  uint16_t   TestKeyA, TestKeyB, TestCtrlW ;
extern  uint32_t  TestReg_R ;

//-------------------------------------------------------
void RfTestEntry_021(void) 
{
 OpCtrl=0 ;
 GFlag=0 ;
 //--
 RSSI_STA_L=0x0002 ;
 RSSI_STA_R=0x0002 ;
 RSSI_BufIndex_L=0 ;
 RSSI_BufIndex_R=0 ;
 //--
 InitSPI1_021() ;
 RfDs.IC021Init_L.OpSta= NOT_INIT_STA ;//
 RfDs.IC021Init_R.OpSta= NOT_INIT_STA ;//
 SYS_STA=0x0006 ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  switch(SYS_STA) {
    case 0x0000: //Select test mode
	     if((TestCtrlW&0xf000)==0xc000) //
	     {//Enter TX test status
		   RfDs.IC021Init_L.OpReq =(uint16_t)(SET_CODE+SET_UP_RX) ;//Request to do Rx setup operation
		   RfDs.IC021Init_R.OpReq =(uint16_t)(SET_CODE+SET_UP_TX) ;//Request to do Tx setup operation
           SYS_STA=0x0002 ;
		   break ;
		  }
	     if((TestCtrlW&0xf000)==0x8000) //
	     {//Enter RX test status
		   RfDs.IC021Init_L.OpReq =(uint16_t)(SET_CODE+SET_UP_RX) ;//Request to do Rx setup operation
	       RfDs.IC021Init_R.OpReq =(uint16_t)(SET_CODE+SET_UP_RX) ;//Request to do Rx setup operation
           SYS_STA=0x0004 ;
		   break ;
		  }
	    break ;
    case 0x0002: //To do RF TX test
		 if(U_Area.TestCtrlW==0x5a50) //MD40099  TestCtrlW
		 {
		   U_Area.TestCtrlW=0x0101 ;
	       WriteReg_021_R(&TestReg_R);//write Test register R15, to Output the carrier only
		  }
		 if(U_Area.TestCtrlW==0x5a5a) //MD40099  TestCtrlW
		 {
		   U_Area.TestCtrlW=0x0101 ;
           WriteReg_021_R(&APP_PC_p->IC021Def.R15_TEST);//write Test register R15, to normal mode
		  }
	    break ;
    case 0x0004: //To do RF RX test
		 RSSI_Filter_021() ;//Get and calculate the RSSI of IC021
		 U_Area.RSSI_L=RfDs.RSSI_L ;
		 U_Area.RSSI_R=RfDs.RSSI_R ;
	     if(OpCtrl&(CB_RX_OK_L+CB_RX_OK_R))
	       Led_2_on ;//Turn on LED 2
	     else
	       Led_2_off ;//Turn off LED 2
	    break ;
    case 0x0006:
//		 if(SW1_2_sta)
//		   TestCtrlW=0xc000 ;//enter TX test staus
//		 else
//		   TestCtrlW=0x8000 ;//enter RX test staus 
//		 SYS_STA=0x0000 ;//For test mode select
	    break ;
	default : SYS_STA=0x0006 ;
  }
//-----------
  RfDaemon_021_L() ;//When in "IN_TEST_STA" status ,it will call "IC021RxTestDaemon_L()"
  RfDaemon_021_R() ;//When in "IN_TEST_STA" status ,it will call "IC021RxTestDaemon_R()"
//-----------
  if(U_Area.TestCtrlW==0x5a5f)	//MD099
  {
	U_Area.TestCtrlW=0x0101 ;
    if(U_Area.TestCommandW==0xc000)
	{//Request to do Tx test operation
	   TestCtrlW=0xc000 ;
       RfDs.IC021Init_L.OpSta= NOT_INIT_STA ;//
       RfDs.IC021Init_R.OpSta= NOT_INIT_STA ;//
	   SYS_STA=0x0000 ;//change or re_init test mode
	 }
	if(U_Area.TestCommandW==0x8000)
	{//Request to do Rx test operation
	   TestCtrlW=0x8000 ;
       RfDs.IC021Init_L.OpSta= NOT_INIT_STA ;//
       RfDs.IC021Init_R.OpSta= NOT_INIT_STA ;//
	   SYS_STA=0x0000 ;//change or re_init test mode
	 }
   }
  //------------------
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  UXTxP() ;
  //------------------
  if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ;
    Timer10[RUN_IND_TIMER].cv=0 ;
    if(Led_1_sta)//LED1 in ON status
    {
      Led_1_off ;//Turn off RUN LED "D1"
     }
    else
      Led_1_on ;//Turn on RUN LED "D1"
   }
  //--
  KickWatchDog() ;// kick the watchdog
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC); 
 }
}
//------------------------------------------------------------
