#include "IC021_Main.h"
//------------------------------------

/*******************************************************************************
* Function Name  : __IC021_Main
* Description    : This is main function entry for use IC021 type receiver
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void  __IC021_Main(void)
{
 InitSPI1_021() ;//Init the SPI1 for  IC021  operation
 InitRfEnvironment();//
 //----------------
 Timer10[RUN_IND_TIMER].pv=95 ;
 Timer10[RUN_IND_TIMER].cv=0 ;
 Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
 Timer10[CM_TIMER].pv=APP_PB.U1Config.rx_t_out ;//Setup modbus slaver mode communication monitor
 Timer10[CM_TIMER].cv=0 ;
 Timer10[CM_TIMER].csr.csword=0x4000 ; //start Timer10[CM_TIMER]
 //----------------
 #ifndef M_DEBUG
   InitWatchDog() ;//Time out value about 10ms
 #endif	
 //------------------------
 InitTimer3_1uS() ;//Init TIM3 for CPU sweep measure(1=1uS)
 U_Area.HW_Version =ReadVersion_021_L() ;
 U_Area.HW_Version|=ReadVersion_021_R() ;
 U_Area.AreaFlag100=0xF100 ;//MD100
 ParaModifyWait() ;//Wait some time for user to modify FYS35 internal parameter
	
 #ifndef M_DEBUG
   KickWatchDog();//kick the watchdog
 #endif
//--------------------------------------------- 
 if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
   InitCanOpen() ;	
 USART_A_Config((UARTPortConfig *)&APP_PB.U1Config) ;//Re-initialize the uart1 and modbus RTU function
 USART_C_Config((UARTPortConfig *)&APP_PB.U3Config) ;//Re-initialize the uart3 and modbus RTU function
 MPM_Sta=0x0000 ;//	
//---------------------------------------

 SYS_STA=POWER_ON_INIT_STA ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  //----------------------------------
  switch(SYS_STA)
  {
  case DUPLEX_INIT_STA :
	RfDs.IC021Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.IC021Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.IC021Init_L.OpSta=NOT_INIT_STA;//
    RfDs.IC021Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=DUPLEX_RX_STA ;
    break ;
  case DUPLEX_RX_STA :
    if((IsrFlag&IFG_GTOKEN_EN))
    {
	  if(RfDs.RF_TxState_R==TX_IDLE)
	  {
        __disable_irq();//Disable IRQ
        IsrFlag &=~IFG_GTOKEN_EN ;
        RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
        __enable_irq();//Enable IRQ
        if(RfDs.IC021Init_R.OpSta!=IN_RX_STA)
        {
          RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
          RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_TX ;
         }
        else
          RfDs.IC021Init_R.OpReq=SET_UP_TX ;//
        SYS_STA=DUPLEX_TX_STA ;
	   }
     }
	 /*
	if((RfDs.IC021Init_L.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_L==0))
	{//The RF chip may be in wrong status
	   RfDs.DClkCntChk_L=480;
       RfDs.IC021Init_L.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if((RfDs.IC021Init_R.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
	   RfDs.DClkCntChk_R=480 ;
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
	 */
    break ;
  case DUPLEX_TX_STA :
    if(RfDs.RF_TxState_R==TX_FINISHED)
    {
       RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
       RfDs.RF_TxState_R=TX_IDLE ;
       if(RfDs.IC021Init_R.OpSta!=IN_TX_STA)
       {
         RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
         RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;//
        }
       else
         RfDs.IC021Init_R.OpReq=SET_UP_RX ;//
       SYS_STA=DUPLEX_RX_STA ;
     }
	 /*
	if((RfDs.IC021Init_R.OpSta==IN_TX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_TX ;
	 }
	 */
    break ;
  case UNI_INIT_STA :
    Timer10[RUN_IND_TIMER].pv=95 ;
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
    InitSPI1_021() ;//Init the SPI1 for  IC021  operation
	RfDs.IC021Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.IC021Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.IC021Init_L.OpSta=NOT_INIT_STA;//
    RfDs.IC021Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=UNI_RX_STA ;
    break ;
  case UNI_RX_STA :
	if((RfDs.IC021Init_L.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_L==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_L.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if((RfDs.IC021Init_R.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
    break ;
  case POWER_ON_INIT_STA :
    //if((TestKeyA==TEST_KEY_ID_A)&&(TestKeyB==TEST_KEY_ID_B))
	if(SW1_1_sta)  //Use SW1.1 to select enter RF test status
      RfTestEntry_021() ;//This call never return
    SYS_STA=DUPLEX_INIT_STA ;//Enter Duplex RF init status
    break ;
  case WAIT_RFIC_READY_STA:

    break ;
  default :
    SYS_STA=POWER_ON_INIT_STA ;
  }
//--------------------------------------

  RfDaemon_021_L() ;
  RfDaemon_021_R() ;
  RfRxDaemon_021_L() ;
  RfRxDaemon_021_R() ;
  RSSI_Filter_021() ;//Get and calculate the RSSI of IC021
  AccessOPS() ;//For shearer mode OPS receiver function through uart port 3
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  ModProcA(&UC,&ModC) ;//Modbus daemon  using channel C
  UXTxP() ;//
  if(Timer10[CM_TIMER].csr.csbit.q==1)
  {
    OpCtrl&=~CB_MDS_COM_OK ;//Wired communication lost error
   }
  if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
	#ifndef M_DEBUG
    if(RUN_Led_sta)
    {
      if((OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))||(U_Area.WiredComSta&0x0010))//Modbus Master or Slaver communication OK,or CANOpen PDO received normally
        Timer10[RUN_IND_TIMER].pv=5 ;
      else
        Timer10[RUN_IND_TIMER].pv=95 ;
      RUN_Led_off ;//--
     }
    else
    {
      Timer10[RUN_IND_TIMER].pv=5 ;
      RUN_Led_on ;//--
     } 
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
	#endif
   }
  //--
  if((U_Area.PaDat_L.KeyCode!=0)||(U_Area.DD_OpsDat_L.KeyCode!=0))
	L_OP_Led_on ;
  else
    L_OP_Led_off ;
  if((U_Area.PaDat_R.KeyCode!=0)||(U_Area.DD_OpsDat_R.KeyCode!=0))
	R_OP_Led_on ;
  else
    R_OP_Led_off ;
  //--
  if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
  {  
    (*CanFun_fp)() ;//For CAN bus operation
    //CANOpenRxTestProc021() ;
    //CANOpenHostSimProc() ;
   }
  //--
  if(U_Area.TestCtrlW==0x5a5f)//MD99
  {
    U_Area.TestCtrlW=0x5a50 ;
    TestCtrlW=U_Area.TestCommandW ;	//MD100
	RfTestEntry_021() ;//nerver return
   }
//----------------------------------------
  SysConfigDaemon() ;//For parameter change IAP
#ifndef M_DEBUG
  KickWatchDog();//kick the watchdog
#endif   
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC);
  if((CK_HSI_Flag==0)&&(RCC->CR&0x00020000)==0)//system init set to use HSE,but HSE not ready
  {
    SCB->AIRCR = 0x05fa0001;//do system & core reset
   }
 }
}
void  CANOpenRxTestProc021(void) 
{
 uint32_t t_key32 ;	
//--------------------------------
//--- U_Area.KeyComb =U_Area.TPDO_RxRF.KeyCode_L|U_Area.TPDO_RxRF.KeyCode_R ;
 t_key32=U_Area.TPDO_RxRF.KeyCode_R;
 t_key32<<=16 ;
 t_key32+=U_Area.TPDO_RxRF.KeyCode_L ;	
 if(t_key32&APP_PB.EStopCode) //
// if(U_Area.KeyComb&APP_PB.EStopCode) //
 {
   S_Area.EStop_Timer=APP_PB.EStopKeepTime ;//This enable EStop signal to enduring at least 2s
  }
 if(APP_PB.EStopKeepTime)//Keep time valid
 {//enable operation keep delay
   if(S_Area.EStop_Timer)
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
 else
 {//No opeartion keep delay
   if(U_Area.KeyComb&APP_PB.EStopCode) //
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
}
//---------
void  Dat2HostProc_021(void)
{
 if(CRC16WithSeed(0xfa5f,(const uint16_t *)U_Area.DD_OpsArea_L,4)==0)
 {
   U_Area.OPS_TPDO.KeyCode_L=U_Area.DD_OpsDat_L.KeyCode ;
   U_Area.OPS_TPDO.OpsStaW_L=U_Area.DD_OpsDat_L.OpsStaW;
  }
 else
   U_Area.OPS_TPDO.KeyCode_L=0 ;
 U_Area.OPS_TPDO.OpsStaW_L&=0x7fff ;
 if(OpCtrl&CB_OPS_ONLINE_L)
   U_Area.OPS_TPDO.OpsStaW_L|=0x8000 ;
 
 if(CRC16WithSeed(0xf5af,(const uint16_t *)U_Area.DD_OpsArea_R,4)==0)
 {
   U_Area.OPS_TPDO.KeyCode_R=U_Area.DD_OpsDat_R.KeyCode ;
   U_Area.OPS_TPDO.OpsStaW_R=U_Area.DD_OpsDat_R.OpsStaW;
  }
 else
   U_Area.OPS_TPDO.KeyCode_R=0 ;
 U_Area.OPS_TPDO.OpsStaW_R&=0x7fff ;
 if(OpCtrl&CB_OPS_ONLINE_R)
   U_Area.OPS_TPDO.OpsStaW_R|=0x8000 ;
 
//------------------------------------------------------------------- 
 U_Area.RF_TPDO.KeyCode_L=U_Area.PaDat_L.KeyCode ;
 U_Area.RF_TPDO.KeyCode_R=U_Area.PaDat_R.KeyCode ;
 //--
 U_Area.RF_TPDO.PanSta_L=0x00 ;
 if(U_Area.PaDat_L.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_L|=0x80 ;//Battery alarm
 if(U_Area.RSSI_L>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_L|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_L.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_L|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_L) U_Area.RF_TPDO.PanSta_L|=0x10 ;//Transsmiter online flag
 //--
 U_Area.RF_TPDO.PanSta_R=0x00 ;
 if(U_Area.PaDat_R.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_R|=0x80 ;//Battery alarm
 if(U_Area.RSSI_R>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_R|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_R.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_R|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_R) U_Area.RF_TPDO.PanSta_R|=0x10 ;//Transsmiter online flag
 //
 U_Area.RF_TPDO.RSSI_L=(s8) U_Area.RSSI_L ;
 U_Area.RF_TPDO.RSSI_R=(s8) U_Area.RSSI_R ;
//--------------------------------
 U_Area.KeyComb =U_Area.RF_TPDO.KeyCode_L|U_Area.RF_TPDO.KeyCode_R ;
 U_Area.KeyComb |=U_Area.OPS_TPDO.KeyCode_L ;
 U_Area.KeyComb |=U_Area.OPS_TPDO.KeyCode_R ;
 
 if((U_Area.KeyComb+U_Area.RemoteKey)&APP_PB.EStopCode) //
 {
   S_Area.EStop_Timer=APP_PB.EStopKeepTime ;//This enable EStop signal to enduring at least 2s
  }
 if(APP_PB.EStopKeepTime)//Keep time valid
 {//enable operation keep delay
   if(S_Area.EStop_Timer)
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
 else
 {//No opeartion keep delay
   if(U_Area.KeyComb&APP_PB.EStopCode) //
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
}
//---------
void Proc10mS_IC021(void) //Timed process at frequency=100Hz
{
  U_Area.Counter10ms++ ;
  if(S_Area.EStop_Timer)
    S_Area.EStop_Timer--;
  if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
  {  
    Dat2HostProc_021() ;//
    Canopen2DisplayBuf() ;
   }
}

void Proc100mS_IC021(void)
{
  RfDs.DClkCntChk_L=RfDs.DClkCnt_L ;
  RfDs.DClkCnt_L=0;  
  if((RfDs.DClkCntChk_L<420)||(RfDs.DClkCntChk_L>520))
    RfDs.DClkCntChk_L=0 ;//Set to invalid

  RfDs.DClkCntChk_R=RfDs.DClkCnt_R ;
  RfDs.DClkCnt_R=0;  
  if((RfDs.DClkCntChk_R<420)||(RfDs.DClkCntChk_R>520))
    RfDs.DClkCntChk_R=0 ;//Set to invalid
  //--
  CANErrMonitor();
}

void Proc1S_IC021(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_IC021(void) //Timed process at interval 1 hour
{
  ;
}

