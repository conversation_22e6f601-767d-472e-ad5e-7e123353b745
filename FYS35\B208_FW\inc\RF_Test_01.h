/************RF_Test_01.h*******************/
#ifndef __RF_TEST_01_H
#define __RF_TEST_01_H
#include "stm32f10x.h"
//-------------------------------------------------------------------------

void RfTestEntry_01(void) ;

void SetupTestRegs_01_L(void) ;
void SetupTestRegs_01_R(void) ;

uint8_t JF01WriteFIFO_Const_L(uint8_t Data ,uint8_t Len) ;
uint8_t JF01WriteFIFO_Const_R(uint8_t Data ,uint8_t Len) ;

__inline void EnterTxTestMode_01_L(void);
__inline void EnterTxTestMode_01_R(void);

__inline void EnterRxTestMode_01_L(void);
__inline void EnterRxTestMode_01_R(void);

void RxTestDaemon_01_L(void);
void RxTestDaemon_01_R(void);

void TxTestDaemon_01_L(void);
void TxTestDaemon_01_R(void);

void  RSSI_Filter_Test_01(void);
int16_t ReadRSSI_Test_01_L(void) ;
int16_t ReadRSSI_Test_01_R(void) ;
//-------------------------------------------------------------------------
#endif   //__RF_TEST_01_H
