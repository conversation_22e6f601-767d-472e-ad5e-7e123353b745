/************TimerCounter.h*******************/
#ifndef __TIMER_H
#define __TIMER_H
#include "stm32f10x.h"
/*timer related const define */

#define  TIM_NUM         2 /*Number of timers in the system  */
#define  TIM10ms_NUM     3 /*Number of timer in 10ms,these timers be processed in user mode*/
#define  TIM1000_NUM     1 /*Number of timer in 1000ms,these timers be processed in user mode*/
//------------------------------------------------------
#define  RUN_IND_TIMER   0 //Timer10 for RUN indicator
#define  CM_TIMER        1 //Timer10 for RS485 communication monitor
#define  PM_TIMER        2 //Timer10 for parameter modify operation monitor

/*********timer function related data define **************/
typedef struct csbitTag
      {
        uint16_t   flag: 1;//when a timer being used set to 1
        uint16_t    tid: 8;
        uint16_t   trig: 1;
        uint16_t unhold: 1;//when set timer continue to run to TC_MAX even cv>=pv
        uint16_t   rev1: 1;//
        uint16_t      q: 1;//Output status bit
        uint16_t   preq: 1;//Previous output stauts
        uint16_t     en: 1;//enable the timer
        uint16_t   rev2: 1;//
       } csbits;
typedef union tc_csunionTag
{    csbits      csbit ;
     uint16_t   csword ;
  } tc_cs;
typedef struct tim_countTag
 {
    tc_cs              csr;
    uint16_t                 pv;
    uint16_t                 cv;
 }  timer_t ;


/*******************************/
/*begin to define timer function*/
extern void TBM_ISR(void) ;
void InitSysTimer(void) ;
void InitTimer(void) ;
void TimerProc(void) ;//user mode timer proc
//-------------------------------------------------------------------------
#endif   //__TIMER_H

