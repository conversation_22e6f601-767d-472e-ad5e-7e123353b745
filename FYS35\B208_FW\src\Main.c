/******************** (C) COPYRIGHT HOME_MADE ********************
* File Name          : main.c
* Author             : Diesel
* Version            : V1.0.1
* Date               : 12/21/2011
* Description        : Wireless receiver main file
********************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "Main.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Extern variables ----------------------------------------------------------*/
extern timer_t Timer[TIM_NUM]  ;

extern timer_t Timer10[TIM10ms_NUM]  ;//user defined timer(1 tick=10ms)

extern timer_t Timer1k[TIM1000_NUM] ;//user defined timer(1 tick=1000ms)

extern  volatile  uint32_t   OpCtrl ;

extern  volatile  uint32_t   GFlag ;
extern  volatile  uint32_t   GFlag_Pre ;

extern  volatile  uint16_t   SYS_STA ;
extern  volatile  int16_t    TxToken ;
extern  volatile  uint16_t   IsrFlag ;//Special flag for used in ISR	,Begin with IFG_
extern  volatile  uint16_t   TestKeyA, TestKeyB, TestCtrlW ;
extern  volatile  uint32_t   CK_HSI_Flag ; //='1' Flag to indicate current use HSI clock,maybe HSE failed to run

extern  SysAreaStruct   S_Area ;//Extern defined an object for system data --in file ".h"

extern  RFC_WorkDS_t      RfDs ;//Main data struct used for RF related function

extern  UserAreaStruct  U_Area ;//Extern defined an object for user data

extern  UARTStruct   UA ;//

extern  ModStruct  ModA ;

/*******************************************************************************
* Function Name  : main.
* Description    : Main routine.
* Input          : None.
* Output         : None.
* Return         : None.
*******************************************************************************/
int main(void)
{
 STM32F_Init();
 //Begin to init application data
 ClrObj16((uint16_t *) &S_Area,sizeof(SysAreaStruct)/2) ;
 ClrObj16((uint16_t *) &U_Area,sizeof(UserAreaStruct)/2) ;
 U_Area.BootCounter=BootDats->rst_count ;
 U_Area.AreaFlag200=0xf200 ;
 //--
 SysConfigInit() ;
	
 InitTimer() ;
 InitSysTimer() ;
	
 if((APP_PA.work_mode&IC_SEL_CONFIG_BIT)==0)
 {//This device be configured to work in IC021 type receiver mode
   AppMain_fp=__IC021_Main ;
   Exi2_isr_fp=NoOperation ;
   Exi3_isr_fp=EXTI3_ISR_021 ;
   Exi9_5_isr_fp=EXTI9_5_ISR_021 ;
   Can1_Rx0_isr_fp=CAN_RX0_ISR_G ;
   Can1_Rx1_isr_fp=CAN_RX1_ISR_G ;
   CanFun_fp=CAN_Proc_G;//CANOpenHostSimProc;//CAN_Proc_JX ; 
   Proc10mS_fp=Proc10mS_IC021 ;
   Proc100mS_fp=Proc100mS_IC021 ;
   Proc1S_fp=Proc1S_IC021 ;
   Proc1H_fp=Proc1H_IC021 ;
   Init_IO_INT_021() ;
   Rf_En_off ;
  }
 else //
 {//This device be configured to work in JF01 type receiver mode
   AppMain_fp=__JF01_Main ;
   Exi2_isr_fp=EXTI2_ISR_01 ;
   Exi3_isr_fp=EXTI3_ISR_01 ;
   Exi9_5_isr_fp=EXTI9_5_ISR_01 ;
   Can1_Rx0_isr_fp=CAN_RX0_ISR_G ;
   Can1_Rx1_isr_fp=CAN_RX1_ISR_G ;
   CanFun_fp=CAN_Proc_G ; 
   Proc10mS_fp=Proc10mS_JF01 ;
   Proc100mS_fp=Proc100mS_JF01 ;
   Proc1S_fp=Proc1S_JF01 ;
   Proc1H_fp=Proc1H_JF01 ;
   Init_IO_INT_01() ;
   Rf_En_off ;
  }
 if (SysTick_Config(SystemCoreClock / 1000))
 { /* Setup SysTick Timer for 1 msec interrupts  */ 
   /* Capture error */ 
   while (1);
  }
/*  
 if(CK_HSI_Flag)//current use HSI,(HSE may start failed)
   InitRfTickTimer_HSI() ;
 else
   InitRfTickTimer() ;
*/
 //--------
#ifndef M_DEBUG 
 if(FLASH_GetReadOutProtectionStatus()==RESET)
 {	 
   FLASH_Unlock();
   FLASH_ReadOutProtection(ENABLE) ;
   FLASH_Lock() ; 
  }
#endif  
 //------Now begin to enter particular operation mode
 (*AppMain_fp)() ;//Normally this function call never return.
}
//-----------------------------END OF MAIN---------------------//

#ifdef USE_FULL_ASSERT
/*******************************************************************************
* Function Name  : assert_failed
* Description    : Reports the name of the source file and the source line number
*                  where the assert_param error has occurred.
* Input          : - file: pointer to the source file name
*                  - line: assert_param error line source number
* Output         : None
* Return         : None
*******************************************************************************/
void assert_failed(uint8_t* file, uint32_t line)
{
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1)
  {}
}
#endif

/*****END OF FILE****/
