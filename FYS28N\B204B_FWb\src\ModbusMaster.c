#include "ModbusMaster.h"

void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum)
{  //Device RS485 communication monitor routine
   if(((*ModX).Err & 0x8000)||((*ModX).Ret & 0x3000))
    {//current poll finished with or without error
     if((*ModX).Err & 0x8000)
      {//No response can be received (may be slaver off line)
        (*ModX).Err &=0x7fff ;
        (*UXModOp).sub_sta=0 ;
       } 

     (*UXModOp).sta++ ;//switch to do next device access

     if((*UXModOp).sta>=DevNum)
        (*UXModOp).sta=0;  
     (*UXModOp).sub_sta=0 ;//switch to proces next device access request status
    }
    if((*ModX).Err & 0x4000)
      {
        (*ModX).Err &=0xbfff ; 
        ++(*UXModOp).ot_err ;//record the over time error
       }
    if((*ModX).Err & 0x0100)
      {
        (*ModX).Err &=0xfeff ; 
        ++(*UXModOp).crc_err ;//record the CRC error
       }
}
//------------------------------------------------
void ActiveReceiver(void)
{
 switch(U_Area.U3ModOp.sta)
 {
  case 0 : { //Send message to extern RF receiver 
            if((U_Area.U3ModOp.sub_sta==0)&&(ModC.Sta==0))
             {
			    ModC.Add=HOST_ID ;//0x05 for monitor computer
			    ModC.Fun=ECS_MWRITE  ;//write slaver 
			    ModC.RegAdd=0x0000   ; //data write to Slaver(MODBUS address 40001)
			    ModC.DatNum=8     ; //output message length
			    ModC.SDp=(uint8_t *) &U_Area.PaDat_L ;//source data,received RF control data 
			    ModC.Sta=0x80 ;//switch to master poll status
			    ModC.Opc=0x80 ; 
			    ModC.Ret=0 ;//reset protocol return value to NULL status
			    ModC.Err=0 ;//clear previous error flag
			    U_Area.U3ModOp.Packages++ ; 
			    U_Area.U3ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModC.Err & 0x8000)||(ModC.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModC.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModC,&U_Area.U3ModOp,2) ;
              }              
            break ;}
  case 1 : { //Read data in external RF receiver 
            if((U_Area.U3ModOp.sub_sta==0)&&(ModC.Sta==0))
             {
			    ModC.Add=HOST_ID ;//0x05 for monitor computer
			    ModC.Fun=ECS_RDHR  ;//read slaver(control host) hold register
			    ModC.RegAdd=0x0010 ; //data Read from Slaver(MODBUS address 40017)
			    ModC.DatNum=20    ; //input message length
		        ModC.RSDp=(uint8_t *) &U_Area.DisplayDatBuf ;//Used to store,received data object
			    ModC.Sta=0x80 ;//switch to master poll status
			    ModC.Opc=0x80 ; 
			    ModC.Ret=0 ;//reset protocol return value to NULL status
			    ModC.Err=0 ;//clear previous error flag
			    U_Area.U3ModOp.Packages++ ; 
			    U_Area.U3ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModC.Err & 0x8000)||(ModC.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModC.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModC,&U_Area.U3ModOp,2) ;
              }              
            break ;}
  default : {
             U_Area.U3ModOp.sta=0 ;
             }  
   }
}
//-----------
void AccessOPS(void)//
{
 U_Area.ToOpsDat_L.LinkCtrlStaW=0x0003 ;
 U_Area.ToOpsDat_L.MachSta=0x5a5a ;
 U_Area.ToOpsDat_L.Dat_1=0x0001 ;
 U_Area.ToOpsDat_L.Dat_2=0x0002 ;
 
 U_Area.ToOpsDat_R.LinkCtrlStaW=0x8003 ;
 U_Area.ToOpsDat_R.MachSta=0xaaaa ;
 U_Area.ToOpsDat_R.Dat_1=0x8001 ;
 U_Area.ToOpsDat_R.Dat_2=0x8002 ;
 switch(U_Area.U3ModOp.sta)
 {
  case 0 : {  //access SLAVER
            if(U_Area.U3ModOp.sub_sta==0)
             {
			    ModC.Add=ID_OPS_L ;//for left side OPS
			    ModC.Fun=ECS_RD_WR    ;//read and write OPS internal register
			    ModC.RegAdd=M_WR_ADD_L ; //data write to OPS
			    ModC.DatNum=M_WR_LEN_L ; //Output message length
                ModC.SDp=(uint8_t *) &U_Area.Data2OpsArea_L[0] ;//source data will ouptput to OPS
                ModC.RAdd=M_RD_ADD_L  ; //Read address in OPS
                ModC.RNum=M_RD_LEN_L  ; //Read message length
			    ModC.RSDp=(uint8_t *) &U_Area.DD_OpsArea_L[0] ;//memory used to save read back data
			    ModC.Sta=0x80 ;//switch to master poll status
			    ModC.Opc=0x80 ;
			    ModC.Ret=0 ;//reset protocol return value to NULL status
				ModC.Err=0 ;
			    U_Area.U3ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModC.Err & 0x8000)||(ModC.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModC.Ret & 0x2000)
			      {//response can be received (slaver on line)
                    //----
                    U_Area.DD_OpsDat_L.OpsStaW^=U_Area.DD_OpsDat_L.CRC_app;
                    U_Area.DD_OpsDat_L.KeyCode^=U_Area.DD_OpsDat_L.CRC_app;
                    U_Area.DD_OpsDat_L.RandomNum^=U_Area.DD_OpsDat_L.CRC_app;
                    //----
                    OpCtrl|=CB_OPS_ONLINE_L ;
			        U_Area.WiredComSta |=0x0001 ;//Set respone OK flag
			        U_Area.U3ModOp.Packages++ ;
			       }
			     else
			      {//receive error or time out
                    ClrObj((uint8_t *) &U_Area.DD_OpsDat_L,M_RD_LEN_L*2) ;//Clear received data
                    OpCtrl&=~CB_OPS_ONLINE_L ;
			        U_Area.WiredComSta &=0xfffe ;//reset response OK flag
			       }
			    }
               ModDevComMon(&ModC,&U_Area.U3ModOp,2) ;
              }
            break ;}
  case 1 : {  //access SLAVER
            if(U_Area.U3ModOp.sub_sta==0)
             {
			    ModC.Add=ID_OPS_R ;//for right side OPS
			    ModC.Fun=ECS_RD_WR    ;//read and write OPS internal register
			    ModC.RegAdd=M_WR_ADD_R ; //data write to OPS
			    ModC.DatNum=M_WR_LEN_R ; //Output message length
                ModC.SDp=(uint8_t *) &U_Area.Data2OpsArea_R[0] ;//source data will ouptput to OPS
                ModC.RAdd=M_RD_ADD_R  ; //Read address in OPS
                ModC.RNum=M_RD_LEN_R  ; //Read message length
			    ModC.RSDp=(uint8_t *) &U_Area.DD_OpsArea_R[0] ;//memory used to save read back data
			    ModC.Sta=0x80 ;//switch to master poll status
			    ModC.Opc=0x80 ;
			    ModC.Ret=0 ;//reset protocol return value to NULL status
				ModC.Err=0 ;
			    U_Area.U3ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModC.Err & 0x8000)||(ModC.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModC.Ret & 0x2000)
			      {//response can be received (slaver on line)
                    //-------
                    U_Area.DD_OpsDat_R.OpsStaW^=U_Area.DD_OpsDat_R.CRC_app ;
                    U_Area.DD_OpsDat_R.KeyCode^=U_Area.DD_OpsDat_R.CRC_app ;
                    U_Area.DD_OpsDat_R.RandomNum^=U_Area.DD_OpsDat_R.CRC_app ;
                    //-------
                    OpCtrl|=CB_OPS_ONLINE_R ;
			        U_Area.WiredComSta |=0x0002 ;//Set respone OK flag
			        U_Area.U3ModOp.Packages++ ;
			       }
			     else
			      {//receive error or time out
                    ClrObj((uint8_t *) &U_Area.DD_OpsDat_R,M_RD_LEN_R*2) ;//Clear received data
                    OpCtrl&=~CB_OPS_ONLINE_R ;
			        U_Area.WiredComSta &=0xfffd ;//reset response OK flag
			       }
			    }
               ModDevComMon(&ModC,&U_Area.U3ModOp,2) ;
              }
            break ;}
  default : {
             U_Area.U3ModOp.sta=0 ;
             }
   }
}
//-----------
void AccessHostPLC(void)
{
 switch(U_Area.U3ModOp.sta)
 {
  case 0 : { //Send message to host PLC
            if((U_Area.U3ModOp.sub_sta==0)&&(ModC.Sta==0))
             {
			    ModC.Add=ID_HOST_PLC ;//0x05 for monitor computer
			    ModC.Fun=ECS_MWRITE  ;//write slaver 
			    ModC.RegAdd=0x0000   ; //data write to Slaver(MODBUS address 40001)
			    ModC.DatNum=8     ; //output message length
			    ModC.SDp=(uint8_t *) &U_Area.RF_PDODat[0] ;//source data,received RF control data and OPS control data and status 
			    ModC.Sta=0x80 ;//switch to master poll status
			    ModC.Opc=0x80 ; 
			    ModC.Ret=0 ;//reset protocol return value to NULL status
			    ModC.Err=0 ;//clear previous error flag
			    U_Area.U3ModOp.Packages++ ; 
			    U_Area.U3ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModC.Err & 0x8000)||(ModC.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModC.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModC,&U_Area.U3ModOp,1) ;
              }              
            break ;}
  default : {
             U_Area.U3ModOp.sta=0 ;
             }  
   }
}
//-----------
