#include "MA_Certify_Fun.h"

void MA_GetRemoteKey(void)
{
   __IO uint8_t *RDp;
   uint8_t tmp_c,i ;
   uint16_t tmp ;
   if(Timer10[CM_TIMER].csr.csbit.q==1)
   {
     U_Area.RemoteKey=0 ;
     OpCtrl&=~CB_REMOTE_KEY ;
    }
/************* begin to check receive buffer****************/
    tmp_c=UC.rxd_sta & 0x77 ;
    if(tmp_c==0)  return  ; //No new data have been received and saved to receive buffer
	if(tmp_c>=0x10)                //Valide data in second receive buffer of channel A
	 {
	   RDp=UC.RxBuf1 ;
	   i=UC.RxDL1 ;
       if(i>2) return ;
	   __disable_irq() ;
       UC.rxd_sta &=0x8f ;//set the buffer to empty status
	   __enable_irq() ;
	  }
	else                          //Valide data in first receive buffer of channel A
	 {
	   RDp=UC.RxBuf0 ;
	   i=UC.RxDL0 ;
       if(i>2) return ;
	   __disable_irq() ;
	   UC.rxd_sta &=0xf8 ;       //set the buffer to empty status
	   __enable_irq() ;
	  }
    if(UC.tcs!=0)
      {          //when receiving can be finished and checked before
                 //TX operation switch to idle status((*UX).tcs==00),can use this method to
        return ; //avoid recieve data send by myself in halfduplex mode
       }
    if(i==2)
    {
      tmp=*RDp ;
      tmp<<=8 ;
      tmp|=*(RDp+1) ;
      U_Area.RemoteKey=tmp ;//Set remote keycode
      Timer10[CM_TIMER].csr.csword=0x4000 ;//reset the monitor timer flag
      Timer10[CM_TIMER].cv=0 ;
      OpCtrl|=(CB_REMOTE_KEY+CB_MDS_COM_OK) ;
    }
}
//--------
void MA_SendTo_PLC(void)
{
 if((OpCtrl&(CB_RX_OK_L+CB_RX_OK_R+CB_REMOTE_KEY))!=0)
 {
  if(UA.tcs==U_TX_IDLE)
  {
    //U_Area.KeyComb --set in FYS30--Proc10mS_S30 function
	Tx_UA((volatile uint8_t *)&U_Area.RemoteKey,4);//including U_Area.KeyComb in last 2 bytes
   }
  }
}
//--------

