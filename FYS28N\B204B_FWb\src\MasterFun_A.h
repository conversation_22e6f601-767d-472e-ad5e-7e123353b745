
//#define ID_HOST_PLC  0x05    //Node ID for host control PLC
//#define ID_OPS_RX    0x05    //Node ID for FYS28N work as OPS signal receiver station
//#define ID_OPS_L     0x06    //Node ID for FYS30 work as left side OPS 
//#define ID_OPS_R     0x07    //Node ID for FYS30 work as right side OPS

#define M_WR_ADD_L    28  //Master intended write register address--40028
#define M_WR_LEN_L    4  //Master intended write register number(length)
#define M_RD_ADD_L    16  //Master intended read register address--40017
#define M_RD_LEN_L    4  //Master intended read register number(length)

#define M_WR_ADD_R    32  //Master intended write register address--40033
#define M_WR_LEN_R    4  //Master intended write register number(length)
#define M_RD_ADD_R    20  //Master intended read register address--40021
#define M_RD_LEN_R    4  //Master intended read register number(length)

void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,unsigned DevNum) ;

void InitU1DeviceOp(void)
{
  uint8_t i,*tp ;
  tp=(uint8_t *) &U_Area.U1ModOp ;
  for(i=0 ;i<sizeof(U_Area.U1ModOp) ;i++)
  {
    *(tp++)=0 ;//clear all related data
   }
}

void AccessOPS(void)//
{
 U_Area.M_RoDat_L.LinkCtrlStaW=0x0003 ;
 U_Area.M_RoDat_L.MachSta=0x5a5a ;
 U_Area.M_RoDat_L.Dat_1=0x0001 ;
 U_Area.M_RoDat_L.Dat_2=0x0002 ;
 
 U_Area.M_RoDat_R.LinkCtrlStaW=0x8003 ;
 U_Area.M_RoDat_R.MachSta=0xaaaa ;
 U_Area.M_RoDat_R.Dat_1=0x8001 ;
 U_Area.M_RoDat_R.Dat_2=0x8002 ;
 switch(U_Area.U1ModOp.sta)
 {
  case 0 : {  //access SLAVER
            if(U_Area.U1ModOp.sub_sta==0)
             {
			    ModB.Add=ID_OPS_L ;//for left side OPS
			    ModB.Fun=ECS_RD_WR    ;//read and write OPS internal register
			    ModB.RegAdd=M_WR_ADD_L ; //data write to OPS
			    ModB.DatNum=M_WR_LEN_L ; //Output message length
                ModB.SDp=(uint8_t *) &U_Area.MDataArea_L[0] ;//source data will ouptput to OPS
                ModB.RAdd=M_RD_ADD_L  ; //Read address in OPS
                ModB.RNum=M_RD_LEN_L  ; //Read message length
			    ModB.RSDp=(uint8_t *) &U_Area.OpsArea_L[0] ;//memory used to save read back data
			    ModB.Sta=0x80 ;//switch to master poll status
			    ModB.Opc=0x80 ;
			    ModB.Ret=0 ;//reset protocol return value to NULL status
				ModB.Err=0 ;
			    U_Area.U1ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModB.Err & 0x8000)||(ModB.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModB.Ret & 0x2000)
			      {//response can be received (slaver on line)
                    //----
                    U_Area.OpsDat_L.OpsStaW^=U_Area.OpsDat_L.CRC_app;
                    U_Area.OpsDat_L.KeyCode^=U_Area.OpsDat_L.CRC_app;
                    U_Area.OpsDat_L.RandomNum^=U_Area.OpsDat_L.CRC_app;
                    //----
                    OpCtrl|=CB_OPS_ONLINE_L ;
			        U_Area.WiredComSta |=0x0002 ;//Set respone OK flag
			        U_Area.U1ModOp.Packages++ ;
			       }
			     else
			      {//receive error or time out
                    ClrObj((uint8_t *) U_Area.OpsArea_L,M_RD_LEN_L*2) ;//Clear received data
                    OpCtrl&=~CB_OPS_ONLINE_L ;
			        U_Area.WiredComSta &=0xfffd ;//reset response OK flag
			       }
			    }
               ModDevComMon(&ModB,&U_Area.U1ModOp,2) ;
              }
            break ;}
  case 1 : {  //access SLAVER
            if(U_Area.U1ModOp.sub_sta==0)
             {
			    ModB.Add=ID_OPS_R ;//for right side OPS
			    ModB.Fun=ECS_RD_WR    ;//read and write OPS internal register
			    ModB.RegAdd=M_WR_ADD_R ; //data write to OPS
			    ModB.DatNum=M_WR_LEN_R ; //Output message length
                ModB.SDp=(uint8_t *) &U_Area.MDataArea_R[0] ;//source data will ouptput to OPS
                ModB.RAdd=M_RD_ADD_R  ; //Read address in OPS
                ModB.RNum=M_RD_LEN_R  ; //Read message length
			    ModB.RSDp=(uint8_t *) &U_Area.OpsArea_R[0] ;//memory used to save read back data
			    ModB.Sta=0x80 ;//switch to master poll status
			    ModB.Opc=0x80 ;
			    ModB.Ret=0 ;//reset protocol return value to NULL status
				ModB.Err=0 ;
			    U_Area.U1ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModB.Err & 0x8000)||(ModB.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModB.Ret & 0x2000)
			      {//response can be received (slaver on line)
                    //-------
                    U_Area.OpsDat_R.OpsStaW^=U_Area.OpsDat_R.CRC_app ;
                    U_Area.OpsDat_R.KeyCode^=U_Area.OpsDat_R.CRC_app ;
                    U_Area.OpsDat_R.RandomNum^=U_Area.OpsDat_R.CRC_app ;
                    //-------
                    OpCtrl|=CB_OPS_ONLINE_R ;
			        U_Area.WiredComSta |=0x0002 ;//Set respone OK flag
			        U_Area.U1ModOp.Packages++ ;
			       }
			     else
			      {//receive error or time out
                    ClrObj((uint8_t *)U_Area.OpsArea_R,M_RD_LEN_R*2) ;//Clear received data
                    OpCtrl&=~CB_OPS_ONLINE_R ;
			        U_Area.WiredComSta &=0xfffd ;//reset response OK flag
			       }
			    }
               ModDevComMon(&ModB,&U_Area.U1ModOp,2) ;
              }
            break ;}
  default : {
             U_Area.U1ModOp.sta=0 ;
             }
   }
}
void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,unsigned DevNum)
{  //Device RS485 communication monitor routine
   if(((*ModX).Err & 0x8000)||((*ModX).Ret & 0x3000))
    {//current poll finished with or without error
     if((*ModX).Err & 0x8000)
      {//No response can be received (may be slaver off line)
        (*ModX).Err &=0x7fff ;
        ++(*UXModOp).norp_err ;
        (*UXModOp).sub_sta=0 ;
       }
     (*UXModOp).sta++ ;//switch to do next device access
     if((*UXModOp).sta>=DevNum)
        (*UXModOp).sta=0;
     (*UXModOp).sub_sta=0 ;//switch to proces next device access request status
    }
    if((*ModX).Err & 0x4000)
      {
        (*ModX).Err &=0xbfff ;
        ++(*UXModOp).ot_err ;//record the over time error
       }
    if((*ModX).Err & 0x0100)
      {
        (*ModX).Err &=0xfeff ;
        ++(*UXModOp).crc_err ;//record the CRC error
       }
}
