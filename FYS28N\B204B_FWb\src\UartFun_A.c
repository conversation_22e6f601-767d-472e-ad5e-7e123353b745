/*********Function related to use UART  communication ******/
//define status varible
#include "Timer.h"
#include "SysConfig.h"
#include "UartFun_A.h"

/* Extern variables ----------------------------------------------------------*/
extern   uint32_t APB1_Clock ;//defined in "system_stm32f10xc"
extern   uint32_t APB2_Clock ;//defined in "system_stm32f10xc"

extern const uint16_t wCRCTable[] ;

extern  SysConfigB_t    APP_PB ;//Application parameter Group B, ID=0x0032

//---------------------------------------------
uint8_t  U1_RX_BUF0[U_DB_MAX]  ;
uint8_t  U1_RX_BUF1[U_DB_MAX]  ;
union {
       uint8_t  U1_TX_BUF[U_DB_MAX]   ;
       uint8_t  U1_MOD_BUF[U_DB_MAX]  ;
      } TxModBuf1 ;

uint8_t  U3_RX_BUF0[U_DB_MAX]  ;
uint8_t  U3_RX_BUF1[U_DB_MAX]  ;
union {
       uint8_t  U3_TX_BUF[U_DB_MAX]   ;
       uint8_t  U3_MOD_BUF[U_DB_MAX]  ;
      } TxModBuf3 ;

//---------------------------------------------
timer_t U_Timer[U_TIM_NUM]  ;

UARTStruct  UA,UC,*UX  ;//

ModStruct  ModA,ModC,*ModX  ;

//---------------------------------------------
void InitUartTimer(void)
{
  unsigned i ;
  for(i=0;i<U_TIM_NUM ;i++)
  {
    U_Timer[i].pv=0 ;
    U_Timer[i].cv=0 ;
    U_Timer[i].csr.csword=0 ;
  }
}

void UartTimerFun(void)//Processed in System tick ISR
{
 unsigned  i ;
 for(i=0;i<U_TIM_NUM;i++)
 {
   if(U_Timer[i].csr.csbit.en==1)
   {
     if(U_Timer[i].cv<65535) ++(U_Timer[i].cv) ;
     if(U_Timer[i].cv>=U_Timer[i].pv)
     {
       U_Timer[i].csr.csbit.q=1 ;
      }
    }
  }
}

//---------------------------------------------
void USART1_BaudRateConfig(USART_TypeDef *UARTx, uint16_t BaudRate)
{
   uint32_t IntegerDivider = 0;
   uint32_t FractionalDivider = 0;
   uint32_t tempreg=0 ;
	
   if((BaudRate>1152)||(BaudRate<12)||(BaudRate%12!=0))
   {//Set to default 19200bps
     /* Determine the integer part */
     if(APB2_Clock<=36000000)
     {
       IntegerDivider =100 ;
       IntegerDivider *=APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider /=19200 ;
       tempreg=IntegerDivider / 100;
      }
     else
     {
       IntegerDivider =APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider *=100 ;
       IntegerDivider /=19200 ;
       tempreg=IntegerDivider / 100;
      }
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
    }
   else
   {
     /* Determine the integer part */
     if(APB2_Clock<=36000000)
     {
       //IntegerDivider =100 ;
       IntegerDivider =APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider /=BaudRate ;
       tempreg=IntegerDivider / 100;
      }
     else
     {
       IntegerDivider =APB2_Clock ;
       IntegerDivider /=16 ;
       //IntegerDivider *=100 ;
       IntegerDivider /=BaudRate ;
       tempreg=IntegerDivider / 100;
      }
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
    }
}
void UART_BaudRateConfig(USART_TypeDef *UARTx, uint16_t BaudRate)
{
   uint32_t IntegerDivider = 0;
   uint32_t FractionalDivider = 0;
   uint32_t tempreg=0 ;
   if((BaudRate>1152)||(BaudRate<12)||(BaudRate%12!=0))
   {//Set to default 19200bps
     /* Determine the integer part */
     IntegerDivider =100 ;
     IntegerDivider *=APB1_Clock ;//The APB1_Clock must not great than 36MHz
     IntegerDivider /=16 ;
     IntegerDivider /=19200 ;
     tempreg=IntegerDivider / 100;
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
   }
   else
   {
     /* Determine the integer part */
     //IntegerDivider =100 ;
     IntegerDivider =APB1_Clock ;
     IntegerDivider /=16 ;
     IntegerDivider /=BaudRate ;
     tempreg=IntegerDivider / 100;
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
    }
}
//---------------------------------------
void USART_A_Config(UARTPortConfig  *pUxConfig)
{
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_USART1, ENABLE);//Reset USART 2
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_USART1, DISABLE);
  NVIC->ICER[(USART1_IRQn>> 0x05)] =(uint32_t)0x01 << (USART1_IRQn& (uint8_t)0x1F);//Disable the USART1 IRQ channel for interrupt
  USART1->CR1 =0x00002000 ;//enable USART1 to function
// Configure the GPIO pins
  GPIOB->CRL &=0x00ffffff ;//Only change PB.6,7
  GPIOB->CRL |=0x89000000 ;//0x89000000
  GPIOB->BSRR =0x000000c0 ;//PB.6,7(U1_TX,U1_RX)=1
//UART peripheral configuration -------------------------------------------
  USART1->CR2   =0x00000000 ;//
  USART1->CR3   =0x00000000 ;//Disable DMA Enabled for TX ,Error interrupt enabled in DMA mode
  /*Configure the UART X */
  if(pUxConfig->parity==0)
    USART1->CR1=0x00002010 ;//UART_NO_PARITY, 8 bit data,enable TXE, RXNE
  else if(pUxConfig->parity==2)
    USART1->CR1=0x00003410 ;//UART_EVEN_PARITY, 9 bits data(one for parity),enable IDLE INT
  else
    USART1->CR1=0x00003610 ;//UART_ODD_PARITY, 9 bits data(one for parity),enable IDLE INT
  /* Configure the BaudRate --------------------------------------------------*/
  USART1_BaudRateConfig(USART1, (uint16_t) pUxConfig->sci_speed) ;
    
//--------------------------------------------------------------------------
  UA.RxBuf0=U1_RX_BUF0 ;
  UA.RxBuf1=U1_RX_BUF1 ;
  UA.TxBuf=TxModBuf1.U1_TX_BUF ;
  UA.tdp=UA.TxBuf ;
  UA.rdp=UA.RxBuf0 ;
  UA.rxd_sta=0x08 ;//set first receive buffer data  is invalid ,second is empty,0x08--data valid
                   //0x00-- buffer empty
  UA.rdl=0 ;
  UA.RxDL0=0 ;
  UA.RxDL1=0 ;
  UA.tcs=0 ;
  UA.tdl=0 ;
//--------------------------------------------------------------------------
  UA.TimerID=UA_TIMER ;
  if(pUxConfig->rx_t_out>=40)
    UA.RWtime=pUxConfig->rx_t_out ;
  else
    UA.RWtime=200 ;
  if((pUxConfig->tx_delay>1)&&(pUxConfig->tx_delay<51))
    UA.TDtime=pUxConfig->tx_delay ;
  else
    UA.TDtime=2 ;
   
  UA.RxFlag=0 ;
//--------------------------------------------------------------------------
  UA.TxPkCount=0 ;
  UA.RxPkCount=0 ;
//--------------------------------------------------------------------------
  ModA.NodeAdd=pUxConfig->mod_add ;//change according to node configuration--debug test
  ModA.Opc=0x00 ;//change according to node configuration
  ModA.Sta=0x00 ;
  ModA.RTryHold=pUxConfig->retry ;
  ModA.MBuf=(uint8_t *) TxModBuf1.U1_MOD_BUF ;
  ModA.Err= 0;
  if(APP_PB.AccSegCtrlW)
     ModA.AccTable=(AccessCtrlStruct *)&APP_PB.Acc_SCtrl_0 ;//Set access control table for port A(=0,no control)
  else
     ModA.AccTable=0x0000 ;//Set access control table for port A(=0,no control)
  ModA.TXPort=Tx_UA ;
  ModA.DModNotify=DModNotify_UA ;//Salver data area have been modified Notify function
// --------------------Configure the DMA1 channel 6 for Rx
  DMA1_Channel5->CPAR=(uint32_t)(&(USART1->DR)) ;
  DMA1_Channel5->CMAR=(uint32_t) (UA.rdp) ;
  DMA1_Channel5->CNDTR=(uint32_t) U_DB_MAX ;//set DMA RX trasction length
  DMA1_Channel5->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
  DMA1_Channel5->CCR|=0x00000001 ;//Enable the DMA channel
  USART1->CR1 |=((uint16_t)0x0004) ;//Enable USART1 for RX operation
  USART1->CR3 |=0x00c0 ;//Enable DMA1 for Tx & Rx
  NVIC->ICPR[(USART1_IRQn>> 0x05)] =(uint32_t)0x01 << (USART1_IRQn& (uint8_t)0x1F);//Clear pending USART1 IRQ
  NVIC->ISER[(USART1_IRQn>> 0x05)] =(uint32_t)0x01 << (USART1_IRQn& (uint8_t)0x1F);//Enable the USART1 IRQ channel for interrupt
}
void OpenUATx(void)
{
 ;
}
void CloseUATx(void)
{
 ;
}


////////////////////////////////
uint8_t   Tx_UA(__IO uint8_t *Tx_UA_BF,int16_t TxdLen)
{
   if((UA.tcs!=U_TX_IDLE)||(TxdLen==0))  return UA.tcs ;
   if(TxdLen >(uint16_t)U_DB_MAX ) TxdLen=U_DB_MAX ;
   ////////////////////////////
   UA.tdp= Tx_UA_BF ;//load data information
   UA.tdl= TxdLen ;
   if(USART1->SR&0x0080)//TXE=1 Transmit Data Register Empty
    {
      UA.tcs=U_TX_WAIT_FOR_SEND;
      U_Timer[UA.TimerID].pv=UA.TDtime ;
      U_Timer[UA.TimerID].cv=0 ;
      U_Timer[UA.TimerID].csr.csword=0x4000 ;//enable U_Timer[0]
      return 0 ;  //
     }
   return 0xff ;  //
}
/*******************************************************************************
* Function Name  :  DModNotify_UA.
* Description    :  Access Notification for modbus function
* Input          :  Add, current access address begin.
                        Len, access area length
* Return         :  =0,access recognized normally
*******************************************************************************/

uint8_t DModNotify_UA(uint16_t Add ,uint16_t Len)
{
 return 0 ;
}

//---------------------------------------
void USART_C_Config(UARTPortConfig  *pUxConfig)
{
   RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
   RCC_APB1PeriphResetCmd(RCC_APB1Periph_USART3, ENABLE);//Reset USART 2
   RCC_APB1PeriphResetCmd(RCC_APB1Periph_USART3, DISABLE);
   NVIC->ICER[(USART3_IRQn>> 0x05)] =(u32)0x01 << (USART3_IRQn& (uint8_t)0x1F);//Disable the USART3 IRQ channel for interrupt
   USART3->CR1 =0x00002000 ;//enable USART3 to function
// Configure the GPIO pins
   GPIOC->CRH &=0xffff00ff ;//Only change PC.10,11
   GPIOC->CRH |=0x00008900 ;//0x00008900
   GPIOC->BSRR =0x00000c00 ;//PC.10,11(U3_TX,U3_RX)=1
//UART peripheral configuration -------------------------------------------
   USART3->CR2   =0x00000000 ;//
   USART3->CR3   =0x00000000 ;//Disable DMA Enabled for TX ,Error interrupt enabled in DMA mode
  /*Configure the UART X */
   if(pUxConfig->parity==0)
     USART3->CR1=0x00002010 ;//UART_NO_PARITY, 8 bit data,enable TXE, RXNE
   else if(pUxConfig->parity==2)
     USART3->CR1=0x00003410 ;//UART_EVEN_PARITY, 9 bits data(one for parity),enable IDLE INT
   else
     USART3->CR1=0x00003610 ;//UART_ODD_PARITY, 9 bits data(one for parity),enable IDLE INT
  /* Configure the BaudRate --------------------------------------------------*/
   UART_BaudRateConfig(USART3, (uint16_t) pUxConfig->sci_speed) ;
//--------------------------------------------------------------------------
   UC.RxBuf0=U3_RX_BUF0 ;
   UC.RxBuf1=U3_RX_BUF1 ;
   UC.TxBuf=TxModBuf3.U3_TX_BUF ;
   UC.tdp=UC.TxBuf ;
   UC.rdp=UC.RxBuf0 ;
   UC.rxd_sta=0x08 ;//set first receive buffer data  is invalid ,second is empty,0x08--data valid
                    //0x00-- buffer empty
   UC.rdl=0 ;
   UC.RxDL0=0 ;
   UC.RxDL1=0 ;
   UC.tcs=0 ;
   UC.tdl=0 ;
//--------------------------------------------------------------------------
   UC.TimerID=UC_TIMER ;
   if(pUxConfig->rx_t_out>=40)
     UC.RWtime=pUxConfig->rx_t_out ;
   else
     UC.RWtime=200 ;
   UC.TDtime=pUxConfig->tx_delay ;
   UC.RxFlag=0 ;
//--------------------------------------------------------------------------
   UC.TxPkCount=0 ;
   UC.RxPkCount=0 ;
//   UC.CH_IRQ_EN=UART3_IRQ_EN ;//Set the IRQ EN MASK
//--------------------------------------------------------------------------
   ModC.NodeAdd=pUxConfig->mod_add ;//change according to node configuration--debug test

   ModC.Opc=0x00 ;//change according to node configuration
   ModC.Sta=0x00 ;
   ModC.RTryHold=pUxConfig->retry ;
   ModC.MBuf=(uint8_t *) TxModBuf3.U3_MOD_BUF ;
   ModC.Err= 0;
   if(APP_PB.AccSegCtrlW)
      ModC.AccTable=(AccessCtrlStruct *)&APP_PB.Acc_SCtrl_0;//Set access control table for port A(=0,no control)
   else
      ModC.AccTable=0x0000 ;//Set access control table for port C(=0,no control)
   ModC.TXPort=Tx_UC ;
   ModC.DModNotify=DModNotify_UC ;//Salver data area have been modified Notify function
// --------------------Configure the DMA1 channel 6 for Rx
   DMA1_Channel3->CPAR=(u32)(&(USART3->DR)) ;
   DMA1_Channel3->CMAR=(u32) (UC.rdp) ;
   DMA1_Channel3->CNDTR=(u32) U_DB_MAX ;//set DMA RX trasction length
   DMA1_Channel3->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
   DMA1_Channel3->CCR|=0x00000001 ;//Enable the DMA channel
   USART3->CR1 |=((uint16_t)0x0004) ;//Enable USART3 for RX operation
   USART3->CR3 |=0x00c0 ;//Enable DMA1 for Tx & Rx
   NVIC->ICPR[(USART3_IRQn>> 0x05)] =(u32)0x01 << (USART3_IRQn& (uint8_t)0x1F);//Clear pending USART3 IRQ
   NVIC->ISER[(USART3_IRQn>> 0x05)] =(u32)0x01 << (USART3_IRQn& (uint8_t)0x1F);//Enable the USART3 IRQ channel for interrupt
}
////////////////////////////////
uint8_t   Tx_UC(__IO uint8_t *Tx_UB_BF,int16_t TxdLen)
{
   if((UC.tcs!=U_TX_IDLE)||(TxdLen==0))  return UC.tcs ;
   if(TxdLen >(uint16_t)U_DB_MAX ) TxdLen=U_DB_MAX ;
   ////////////////////////////
   UC.tdp= Tx_UB_BF ;//load data information
   UC.tdl= TxdLen ;
   if(USART3->SR&0x0080)//TXE=1 Transmit Data Register Empty
    {
      UC.tcs=U_TX_WAIT_FOR_SEND;
      U_Timer[UC.TimerID].pv=UC.TDtime ;
      U_Timer[UC.TimerID].cv=0 ;
      U_Timer[UC.TimerID].csr.csword=0x4000 ;//enable U_Timer[0]
      return 0 ;  //
     }
   return 0xff ;  //
}
   
void OpenUCTx(void)
{
 ;
}
void CloseUCTx(void)
{
 ;
}
/*******************************************************************************
* Function Name  :  DModNotify_UC.
* Description    :  Access Notification for modbus function
* Input          :  Add, current access address begin.
                        Len, access area length
* Return         :  =0,access recognized normally
*******************************************************************************/

uint8_t DModNotify_UC(uint16_t Add ,uint16_t Len)
{
 return 0 ;
}

//--------------------------------------------
void UXTxP(void)
{
//For UART1
 switch (UA.tcs)
  {
   case U_TX_IDLE : {//#define U_TX_IDLE 0x0000
                     break ;}
   case U_TX_WAIT_FOR_IDLE :
                     UA.tcs=U_TX_IDLE ;
					 UA.TxPkCount++ ;
                     CloseUATx() ;
	                 break ;
   case U_TX_WAIT_FOR_SEND : {//#define U_TX_WAIT_FOR_SEND 0x0002
			         if(U_Timer[UA.TimerID].csr.csbit.q==1)
			         {
                       if(UA.tdl>1)
		                UA.tcs=U_TX_CONTINUE_SEND ;//
                       else
		                UA.tcs=U_TX_WAIT_FOR_FINISH ;//
			           U_Timer[UA.TimerID].csr.csword=0x0000 ;
			           U_Timer[UA.TimerID].cv=0 ;
                       OpenUATx() ;//open UART1 port's tx
                       DMA1_Channel4->CPAR=(uint32_t)(&(USART1->DR)) ;
                       DMA1_Channel4->CMAR=(uint32_t) (UA.tdp) ;
                       DMA1_Channel4->CNDTR=(uint32_t) (UA.tdl) ;//set DMA TX trasction length
                       DMA1_Channel4->CCR=0x00001093 ;//DMA_DIR_PeripheralDST|DMA_MemoryInc_Enable|DMA_Priority_Medium|DMA_IT_TC
                       DMA1_Channel4->CCR|=0x00000001 ;//Enable the DMA channel
                       USART1->CR1 |=((uint16_t)0x0008) ;//USART1 TX enable
			          }
			         break ;}
  case U_TX_CONTINUE_SEND   ://#define U_TX_CONTINUE_SEND   0x04
                     break ;
  case U_TX_WAIT_FOR_FINISH :
                     if(USART1->SR&0x0040)//Tx data register empty & Transmission complete
                     {
                       USART1->SR&=0xffbf ;//Clear Transmission complete flag
                       UA.tcs=U_TX_WAIT_FOR_IDLE ;
                      }
                     break ;
  default : UA.tcs=U_TX_IDLE ;
  }
//For UART3
 switch (UC.tcs)
  {
   case U_TX_IDLE : {//#define U_TX_IDLE 0x0000
                     break ;}
   case U_TX_WAIT_FOR_IDLE :
                     UC.tcs=U_TX_IDLE ;
					 UC.TxPkCount++ ;
                     CloseUCTx() ;
	                 break ;
   case U_TX_WAIT_FOR_SEND : {//#define U_TX_WAIT_FOR_SEND 0x0002
			         if(U_Timer[UC.TimerID].csr.csbit.q==1)
			         {
                       if(UC.tdl>1)
		                UC.tcs=U_TX_CONTINUE_SEND ;//
                       else
		                UC.tcs=U_TX_WAIT_FOR_FINISH ;//
			           U_Timer[UC.TimerID].csr.csword=0x0000 ;
			           U_Timer[UC.TimerID].cv=0 ;
                       OpenUCTx() ;//open UART2 port's tx
                       DMA1_Channel2->CPAR=(u32)(&(USART3->DR)) ;
                       DMA1_Channel2->CMAR=(u32) (UC.tdp) ;
                       DMA1_Channel2->CNDTR=(u32) (UC.tdl) ;//set DMA TX trasction length
                       DMA1_Channel2->CCR=0x00001093 ;//DMA_DIR_PeripheralDST|DMA_MemoryInc_Enable|DMA_Priority_Medium|DMA_IT_TC
                       DMA1_Channel2->CCR|=0x00000001 ;//Enable the DMA channel
                       USART3->CR1 |=((uint16_t)0x0008) ;//USART3 TX enable
			          }
			         break ;}
  case U_TX_CONTINUE_SEND   ://#define U_TX_CONTINUE_SEND   0x04
                     break ;
  case U_TX_WAIT_FOR_FINISH :
                     if(USART3->SR&0x0040)//Tx data register empty & Transmission complete
                     {
                       USART3->SR&=0xffbf ;//Clear Transmission complete flag
                       UC.tcs=U_TX_WAIT_FOR_IDLE ;
                      }
                     break ;
  default : UC.tcs=U_TX_IDLE ;
  }
}

//-----------------------------------------------
extern void USART1_ISR(void)
{
  uint16_t l_tmp ;
  uint16_t s_tmp ;
  l_tmp=USART1->SR ;
  s_tmp=USART1->DR ;//clear the flag ;
  s_tmp=s_tmp ;
  if(l_tmp &0x0010 ) //Idle line recieved
  {
    ++UA.RxPkCount ;
    DMA1_Channel5->CCR=0x00000000 ;//disable the DMA channel5
    DMA1_Channel5->CPAR=(uint32_t)(&(USART1->DR)) ;
    if((UA.rxd_sta & 0x88)==0x08)
    {
       UA.RxDL0=(uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel5->CNDTR) ;
       UA.rxd_sta=0x87 ;//received new data ,old receiving buffer invalid
	 		   	         //set new data life cycle to 7f
       DMA1_Channel5->CMAR=(uint32_t)UA.RxBuf1 ;
     }
    else
    {
      UA.RxDL1=( uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel5->CNDTR) ;
      UA.rxd_sta=0x78 ;//received new data ,old receiving buffer invalid
   		  	        //set new data life cycle to 7f
      DMA1_Channel5->CMAR=(uint32_t)UA.RxBuf0 ;
     }
    UA.rdl=0 ;
    DMA1_Channel5->CNDTR=(uint32_t) U_DB_MAX ;//set DMA RX trasction length
    DMA1_Channel5->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
    DMA1_Channel5->CCR|=0x00000001 ;//Enable the DMA channel
   }
}
//-----------------------------------------------

extern void UART1_DMA_ISR(void)
{
  if(DMA1->ISR&0x00002000)
  {
    DMA1->IFCR=0x00002000 ;
    UA.tcs=U_TX_WAIT_FOR_FINISH ;//
    DMA1->IFCR=0x00000000 ;
    DMA1_Channel4->CCR&=0xfffffffe ;//Disable the DMA channel 4
  }
}

//-----------------------------------------------
extern void USART3_ISR(void)
{
  uint16_t l_tmp ;
  uint16_t s_tmp ;
  l_tmp=USART3->SR ;
  s_tmp=USART3->DR ;//clear the flag ;
  s_tmp=s_tmp ;
  if(l_tmp &0x0010 ) //Idle line recieved
  {
    ++UC.RxPkCount ;
    DMA1_Channel3->CCR=0x00000000 ;//disable the DMA channel 3
    DMA1_Channel3->CPAR=(u32)(&(USART3->DR)) ;
    if((UC.rxd_sta & 0x88)==0x08)
    {
       UC.RxDL0=( uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel3->CNDTR) ;
       UC.rxd_sta=0x87 ;//received new data ,old receiving buffer invalid
	 		   	         //set new data life cycle to 7f
       DMA1_Channel3->CMAR=(u32)UC.RxBuf1 ;
     }
    else
    {
      UC.RxDL1=( uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel3->CNDTR) ;
      UC.rxd_sta=0x78 ;//received new data ,old receiving buffer invalid
   		  	        //set new data life cycle to 7f
      DMA1_Channel3->CMAR=(u32)UC.RxBuf0 ;
     }
    UC.rdl=0 ;
    DMA1_Channel3->CNDTR=(u32) U_DB_MAX ;//set DMA RX trasction length
    DMA1_Channel3->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
    DMA1_Channel3->CCR|=0x00000001 ;//Enable the DMA channel
   }
}
extern void UART3_DMA_ISR(void)
{
  if(DMA1->ISR&0x00000020)
  {
    DMA1->IFCR=0x00000020 ;
    UC.tcs=U_TX_WAIT_FOR_FINISH ;//
    DMA1->IFCR=0x00000000 ;
    DMA1_Channel2->CCR&=0xfffffffe ;//Disable the DMA channel 2
  }
}

//--------------------End of file
