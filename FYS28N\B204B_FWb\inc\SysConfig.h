#ifndef  __SYS_CONFIG_H
#define  __SYS_CONFIG_H
#include "stdint.h"
#include "SysBasic.h"

//System application configuration parameter group A
typedef struct TagSysConfigA_t  //Parameter ID=0x0031 ,LEN=28
{ 
  uint16_t            pid ;//Px0101,Parameter's identifier
  uint16_t            len ;//Px0102,configuration data length
  uint16_t     rev_wa1[2] ;// ,+4

  uint16_t      work_mode ;//Px0105, work mode of this device.=0x0xxx-0x7xxx->for IC021 type receiver  ,=0x8xxx-0xfxxx->for JF01 type receiver
                           //--Bit_11 of this parameter to switch if Heart beat signal used(bit11='1' use heart beat signal,='0' not use it
						   //--Bit_8 K1 relay power on status,=0 in off status after power on(normally off,operation to on),=1 in on status after power on(normally on,Operation to off)
  uint16_t        GroupId ;//Px0106,Bit0-1[0-3]set this panel  in group id,Bit2-7[0-63] group id ,When the device work as panel transmitter 
  uint16_t     rev_wa2[2] ;//Px0107-0108,

  uint16_t     RFIC_L_RxChNum ; //Px0109,Left side RF IC Recieve working frequecy channel number
  uint16_t     RFIC_L_TxChNum ; //Px0110,Left side RF IC Transmitte working frequecy channel number
  uint16_t     RFIC_R_RxChNum ;	//Px0111,Right side RF IC Recieve working frequecy channel number
  uint16_t     RFIC_R_TxChNum ;	//Px0112,Right side RF IC Transmitte working frequecy channel number  ,+4

  uint16_t     AirLinkageTO ;//Px0113, Airlinkage monitor time out, in 10mS unit
  uint16_t      OpcKeepTime ;//Px0115, Operation key code keep time for rf command,in 10 mS
  uint16_t     HeartBeatTim ;//Px0114, Heart beat guard time for monitor the transmitter, 
  uint16_t         TxPreLen ;//Px0116, RF Tx frame preamble segment length in bytes

 uint16_t       SyncTimeSet ;//Px0117,Reset value for RF tick timer,when synchronization signal be captured,will be override use those parameter in APP_PB
 uint16_t     RfTickTimerPV ;//Px0118,Preload counter cycle value for the RF tick timer(determine the RF tick long)
                             //       will be override use those parameter in APP_PB
   uint16_t     rev_wa3[2] ;//Px01119-0120,

  uint16_t  JF01_RxAttenMTO ;//Px0121,JF01 chip Rx attenuation status monitor time out value --in mS ,+4						   
  uint16_t           rev_w1 ;//Px0122,
  int16_t      RSSIOffset_L ;//Px0123,
  int16_t      RSSIOffset_R ;//Px0124, ,+4

 int16_t       TCoeff ;//Px0125,typical temperature coefficient (1.66x4095/3300)*1000
 int16_t      VTemp25 ;//Px0126,25 celsuis degree output offset 
 uint16_t      rev_w2 ;//Px0127,
  uint16_t      crc16 ;//Px0128,CRC-16 of this configuration object   +4
} SysConfigA_t ;

//System application configuration parameter group B
typedef struct TagSysConfigB_t  //Parameter ID=0x0036 ,LEN=32
{ 
 uint16_t         pid ;//Parameter's identifier
 uint16_t         len ;//configuration data length
 uint16_t     ACode_l ;//Px0103,module Auth code low word16
 uint16_t     ACode_h ;//Px0104,module Auth code hight word16   ,+4


 UARTPortConfig      U1Config ;   //Px0105-0108,   +4

 UARTPortConfig      U3Config ;   //Px0109-0112,   +4

 SegCtrlStruct    Acc_SCtrl_0 ;//Px0113-0115,Access  control segment 0 , +3
 SegCtrlStruct    Acc_SCtrl_1 ;//Px0116-0118,Access  control segment 1 , +3
 SegCtrlStruct    Acc_SCtrl_2 ;//Px0119-0121,Access  control segment 2 , +3
 SegCtrlStruct    Acc_SCtrl_3 ;//Px0122-0124,Access  control segment 3 , +3 (+12)

 uint16_t    AccSegCtrlW ;//Px0125,Access control table entry number(=0,no access control)
 uint16_t         rev_w1 ;//Px0126,
 uint16_t    LeftKeyMask ;//Px0127,Left side mode operation key mask code
 uint16_t   RightKeyMask ;//Px0128,Right side mode operation key mask  code,+4

 uint16_t    CAN_Mode ;//Px0129,CAN bus interface working mode 
 uint16_t    CAN_Speed ;//Px0130,CAN bus working speed setting 125kbps,250kbps,500kbps,1000kbps
 uint16_t    rev_w2 ;//Px0131,
 uint16_t    rev_w3 ;//Px0132,  ,+4
 
 uint8_t     CANOpenID ;//Px0133_LB,CANOpen mode Transmitte PDO Node ID 1-127(low byte)
 uint8_t     CANOpen_Set ;//Px0133_HB,CANOpen special setting byte(high byte),if CANOpen_Set&0x80!=0 use CANOpenID as node ID,else use SW3 setting
 uint8_t     CANOpenR_ID_1 ;//Px0134_LB,CANOpen mode receive PDO Node ID 1(low byte)
 uint8_t     CANOpenR_ID_2 ;//Px0134_HB,CANOpen mode receive PDO Node ID 2(high byte) ,+4	
 uint16_t    SYNC_Interval ;//Px0135,CANOpen synchronizing PDO Transmit interval
 uint16_t    PDO_Interval ;//Px0136,,CANOpen data PDO Transmit interval

 uint16_t      SyncTimeSet_021 ;//Px0137,2320--Reset value for RF tick timer,when synchronization signal be captured
 uint16_t    RfTickTimerPV_021 ;//Px0138,Preload counter cycle value for the RF tick timer(determine the RF tick long)
                                 //       for Fyf35 use 3275(10.005Hz)
 uint16_t       SyncTimeSet_01 ;//Px0139,1160--Reset value for RF tick timer,when synchronization signal be captured
 uint16_t     RfTickTimerPV_01 ;//Px0140,Preload counter cycle value for the RF tick timer(determine the RF tick long)
                                //       for Fyf25 use 1638(20.0048Hz)

 uint32_t     EStopCode     ;//Px0141,142,define the application specified emergence stop key code
 uint16_t     EStopKeepTime ;//Px0143, K1 relay operation keep time ,in 10 mS
 uint16_t         crc16 ;//Px0144,CRC-16 of this configuration object   +4
} SysConfigB_t  ;

//System application  configuration parameter group C--Main for 021 RF IC
typedef struct TagSysConfigC_t
{//Parameter ID=0x0033 ,LEN=86
 uint16_t               pid ;//Parameter's identifier
 uint16_t               len ;//configuration data length
 uint16_t         rev_wa[2] ;// ,+4

 IC021Registers          IC021Def ;//default configuration of IC021 registers,15 dwords, +30

 IC021RxSwitchReg   Rx_Freq_Ch[6] ;//Rx frequecy work channel 0-5 register setting,12 dwords,+24

 IC021TxSwitchReg   Tx_Freq_Ch[6] ;//Tx frequecy work channel 0-5 register setting,12 dwords,+24

//-----------------
 uint16_t      uid_l ;//module UID low word16
 uint16_t      uid_h ;//module UID hight word16
 uint16_t      rev_w  ;
 uint16_t      crc16  ;//CRC-16 of this configuration object   +4
} SysConfigC_t  ;

//-----------------------------------------------------------------------------------------------
typedef struct TagSysConfigD_t  //Parameter ID=0x0034 ,LEN=56 main for JF01 IC
{ 
  uint16_t         pid ;//Px0101,Parameter's identifier
  uint16_t         len ;//Px0102,configuration data length
  uint16_t   rev_wa[2] ;// ,+4

  JF01Registers         JF01Def ;//default configuration of JF01 register ,+24

  JF01SwitchReg   Rx_Freq_Ch[6] ;//Rx frequecy work channel 0-5 register setting,+12 

  JF01SwitchReg   Tx_Freq_Ch[6] ;//Tx frequecy work channel 0-5 register setting,+12
   
  uint16_t    rev_wa2[3] ;// 
  uint16_t        crc16  ;//Px0148,CRC-16 of this configuration object   +4
} SysConfigD_t ;
//-----------------------------------------------------------------------------------------------
extern const  uint16_t  SysConfigADef[]	;//Default application parameter group A

extern SysConfigA_t     APP_PA ;//System application parameter Group A, ID=0x0081

extern  SysConfigB_t    APP_PB ;//EDC module  parameter Group B, ID=0x0082

extern SysConfigC_t   *APP_PC_p ;//System application parameter Group C pointer, ID=0x0083

extern SysConfigD_t   *APP_PD_p ;//System application parameter Group D pointer, ID=0x0084

void  ReadFlashPara(uint16_t *buf,uint16_t size,uint16_t pp_num) ;//Read parameter from fixed flash area

void  WriteFlashPara(uint16_t *buf ,uint16_t size,uint16_t pp_num) ;//write data to fixed flash parameter area

void  SysConfigInit(void)  ;

void  ParaModifyWait(void) ;

void  ModbusParaDaemon(void) ;

void SysConfigDaemon(void)  ;

#endif  //__SYS_CONFIG_H

