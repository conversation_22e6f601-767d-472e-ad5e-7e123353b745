/*******ModbusRTU_A.h forModbus RTU function  IPM in ARM in 2G TDECS system********/
#ifndef __MODBUS_RTU_H
#define __MODBUS_RTU_H
#ifndef ECS_FUNC_CODE
  #define ECS_FUNC_CODE  1
  #define ECS_RDHR     0x0003   //define TDECS read register operation code
  #define ECS_RDIR     0x0004   //define TDECS read input register operation code
  #define ECS_WRITE    0x0006   //define TDECS single register write operation code
  #define ECS_LOOP     0x0008   //define TDECS loop back test operation code
  #define ECS_MWRITE   0x0010   //define TDECS multi-register write operation code
  #define ECS_MASK_W   0x0016   //define TDECS mask write a register
  #define ECS_RD_WR    0x0017   //define TDECS read and write register operation code

  #define RFBASE_ADD   20000

  #define COMMAND_ADD   65532 //0xfffc

  #define MREG_4_BASE  (uint32_t) &U_Area  // define modbus protocol holding register base address in the MCU

  #define MREG_4_RFBASE (uint32_t) &RfDs  // define specifial modbus protocol holding register base address for RF related data

  #define MREG_4_LIMIT 0x5000 //define modbus protocol holding register length limit
#endif
/*All words tranmittied with Hi byte first ,then Lo Byte ,except for CRC16 value used in modbus */

#endif //__MODBUS_RTU_H
