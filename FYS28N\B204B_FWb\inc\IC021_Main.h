/************IC021_Main.h*******************/
#ifndef __IC021_MAIN_H
#define __IC021_MAIN_H
#include "stm32f10x.h"
#include "SysBasic.h"
#include "SysConfig.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "CAN_bx.h"
#include "UartFun_A.h"
#include "ModbusMaster.h"
#include "RF_BasicFun.h"
#include "MA_Certify_Fun.h"
//---------------------------------------
extern  timer_t  RfcTimer[RFC_TIMER_NUM] ;//system ISR process

extern  timer_t  RfcTimer10[RFC_TIMER10_NUM] ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint16_t  RssiDisSta ;

extern  volatile  int16_t  SyncTimeOut ;

extern  volatile  int16_t  LCD_BL_Timer ;

extern  volatile  int16_t   RSSI_Sum	;

extern  UARTStruct   UB ;//

extern  ModStruct  ModB ;

extern  uint32_t  ADC_STA ;

//---------------------------------------------
void  __IC021_Main(void) __attribute__ ((noreturn));

void  Dat2HostProc_021(void) ;

void  CANOpenRxTestProc021(void) ;

void  Proc10mS_IC021(void); //Timed process at frequency=100Hz

void  Proc100mS_IC021(void);//Timed process at frequency=10Hz

void  Proc1S_IC021(void);//Timed process at frequency=1Hz

void  Proc1H_IC021(void); //Timed process at interval 1 hour

#endif //__IC021_MAIN_H
