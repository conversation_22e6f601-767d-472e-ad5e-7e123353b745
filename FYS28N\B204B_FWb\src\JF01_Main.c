#include "JF01_Main.h"
extern volatile   int16_t    daemon_lock ;

/*******************************************************************************
* Function Name  : __JF01_Main
* Description    : This is main function entry for JF01 type receiver
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void __JF01_Main(void) 
{
 InitSPI1_01() ;//Init the SPI1 for  JF01  operation
 InitRfEnvironment();//
 //----------------
 Timer10[RUN_IND_TIMER].pv=95 ;
 Timer10[RUN_IND_TIMER].cv=0 ;
 Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
 Timer10[CM_TIMER].pv= 20;//APP_PB.U1Config.rx_t_out ;//Setup modbus slaver mode communication monitor
 Timer10[CM_TIMER].cv=0 ;
 Timer10[CM_TIMER].csr.csword=0x4000 ; //start Timer10[CM_TIMER]
 //----------------
#ifndef M_DEBUG
 InitWatchDog() ;//Time out value about 10ms
#endif	
 //------------------------
 InitTimer3_1uS() ;//Init TIM3 for CPU sweep measure(1=1uS)
 U_Area.AreaFlag100=0xF100 ;//MD0100
//---------------------------------------
 ParaModifyWait() ;//Wait some time for user to modify FYS35 internal parameter
 #ifndef M_DEBUG
   KickWatchDog();//kick the watchdog
 #endif   
//--------------------------------------------- 
 if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
   InitCanOpen() ;	
 USART_A_Config((UARTPortConfig *)&APP_PB.U1Config) ;//Re-initialize the uart1 and modbus RTU function
 USART_C_Config((UARTPortConfig *)&APP_PB.U3Config) ;//Re-initialize the uart3 and modbus RTU function
 MPM_Sta=0x0000 ;//	
 SYS_STA=POWER_ON_INIT_STA ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  //--------------------------------------
  switch(SYS_STA)
  {
  case DUPLEX_INIT_STA :
    RfDs.JF01Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_L.OpSta =NOT_INIT_STA;//
    RfDs.JF01Init_R.OpSta =NOT_INIT_STA;//
    SYS_STA=DUPLEX_RX_STA ;
    break ;
  case DUPLEX_RX_STA :
    if((IsrFlag&IFG_GTOKEN_EN))
    {
	  if(RfDs.RF_TxState_R==TX_IDLE)
	  {
        __disable_irq();//Disable IRQ
        IsrFlag &=~IFG_GTOKEN_EN ;
        RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
        __enable_irq();//Enable IRQ
        if(RfDs.JF01Init_R.OpSta!=IN_RX_STA)
        {
          RfDs.JF01Init_R.OpSta=NOT_INIT_STA ;
          RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_TX ;
         }
        else
          RfDs.JF01Init_R.OpReq=SET_UP_TX ;//
        SYS_STA=DUPLEX_TX_STA ;
	   }
     }
    break ;
  case DUPLEX_TX_STA :
    if(RfDs.RF_TxState_R==TX_FINISHED)
    {
      RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
      RfDs.RF_TxState_R=TX_IDLE ;
      if(RfDs.JF01Init_R.OpSta!=IN_TX_STA)
      {
        RfDs.JF01Init_R.OpSta=NOT_INIT_STA ;
        RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_RX ;//
       }
      else
        RfDs.JF01Init_R.OpReq=SET_UP_RX ;//
      SYS_STA=DUPLEX_RX_STA ;
     }
    break ;
  case UNI_INIT_STA :
	RfDs.JF01Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.JF01Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_L.OpSta=NOT_INIT_STA;//
    RfDs.JF01Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=UNI_RX_STA ;
    break ;
  case UNI_RX_STA :
    /*
	if(RfDs.JF01Init_L.OpSta==IN_RX_STA)
	{//The RF chip may be in wrong status
       RfDs.JF01Init_L.OpSta=NOT_INIT_IDLE ;
       RfDs.JF01Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if(RfDs.JF01Init_R.OpSta==IN_RX_STA)
	{//The RF chip may be in wrong status
       RfDs.JF01Init_R.OpSta=NOT_INIT_IDLE ;
       RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
	 */
    break ;
  case POWER_ON_INIT_STA :
    //if((TestKeyA==TEST_KEY_ID_A)&&(TestKeyB==TEST_KEY_ID_B))
	if(SW1_1_sta)  //Use SW1.1 to select enter RF test status
      RfTestEntry_01() ;//This call never return
    SYS_STA=UNI_INIT_STA;//DUPLEX_INIT_STA ;//Enter Duplex RF init status
    break ;
  case WAIT_RFIC_READY_STA:

    break ;
  default :
    SYS_STA=POWER_ON_INIT_STA ;
  }
  //--
  RfDaemon_01_L() ;
  RfDaemon_01_R() ;
  RfRxDaemon_01_L() ;
  RfRxDaemon_01_R() ;
  RSSI_Filter_01() ;//Get and calculate the RSSI of JF01
  //----------------------------------
#ifdef  MA_TEST_SAMPLE
  MA_SendTo_PLC() ;//Use NON-IS port A
  MA_GetRemoteKey() ;//Receive use IS port C
#else
  //AccessOPS() ;//For shearer mode OPS receiver function through uart port 3
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  ModProcA(&UC,&ModC) ;//Modbus daemon  using channel C
#endif  
  UXTxP() ;//
  //--
  if(Timer10[CM_TIMER].csr.csbit.q==1)
  {
    OpCtrl&=~CB_MDS_COM_OK ;//Wired communication lost error
   }
  if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
    if(RUN_Led_sta)
    {
      if((OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))||(U_Area.WiredComSta&0x0010))//Modbus Master or Slaver communication OK,or CANOpen PDO received normally
        Timer10[RUN_IND_TIMER].pv=5 ;
      else
        Timer10[RUN_IND_TIMER].pv=95 ;
      RUN_Led_off ;//--
     }
    else
    {
      Timer10[RUN_IND_TIMER].pv=5 ;
      RUN_Led_on ;//--
     } 
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
   }
  //--
  if((U_Area.PaDat_L.KeyCode!=0)||(U_Area.DD_OpsDat_L.KeyCode!=0))
	L_OP_Led_on ;
  else
    L_OP_Led_off ;
  if((U_Area.PaDat_R.KeyCode!=0)||(U_Area.DD_OpsDat_R.KeyCode!=0))
	R_OP_Led_on ;
  else
    R_OP_Led_off ;
  //--
  if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
  {  
    (*CanFun_fp)() ;//For CAN bus operation
    //CANOpenHostSimProc() ;
    //CANOpenRxTestProc01() ;
    //Dat2HostProc_01() ;
   }
  //--
  if(U_Area.TestCtrlW==0x5a5f)//MD186
  {
    U_Area.TestCtrlW=0x5a50 ;
    TestCtrlW=U_Area.TestCommandW ;	//MD187
	RfTestEntry_01() ;//nerver return
   }
  if(U_Area.AreaFlag200==0x1234)//MD20356,==0x1234
  {
    U_Area.AreaFlag200=0x4321 ;
    JF01_B_Read_L(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[0],47) ;//---MD201
    JF01_B_Read_R(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[24],47) ;//---MD225
   }
  if(U_Area.AreaFlag200==0x2345)//MD20356,==0x2345
  {
    U_Area.AreaFlag200=0x5432 ;
    JF01_B_Read_L(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[48],1) ;//---MD249HB
    JF01_B_Read_R(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[49],1) ;//---MD250HB
   }
//----------------------------------------
  SysConfigDaemon() ;//For parameter change IAP
#ifndef M_DEBUG
  KickWatchDog();//kick the watchdog
#endif   
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC);
  if((CK_HSI_Flag==0)&&(RCC->CR&0x00020000)==0)//system init set to use HSE,but HSE not ready
  {
   SCB->AIRCR = 0x05fa0001;//do system & core reset
   }
 }
}
//------------------------------------
void  Dat2HostProc_01(void)
{
 uint32_t t_key32 ;	
	
 if(CRC16WithSeed(0xfa5f,(const uint16_t *)U_Area.DD_OpsArea_L,4)==0)
 {
   U_Area.OPS_TPDO.KeyCode_L=U_Area.DD_OpsDat_L.KeyCode ;
   U_Area.OPS_TPDO.OpsStaW_L=U_Area.DD_OpsDat_L.OpsStaW;
  }
 else
   U_Area.OPS_TPDO.KeyCode_L=0 ;
 U_Area.OPS_TPDO.OpsStaW_L&=0x7fff ;
 if(OpCtrl&CB_OPS_ONLINE_L)
   U_Area.OPS_TPDO.OpsStaW_L|=0x8000 ;
 
 if(CRC16WithSeed(0xf5af,(const uint16_t *)U_Area.DD_OpsArea_R,4)==0)
 {
   U_Area.OPS_TPDO.KeyCode_R=U_Area.DD_OpsDat_R.KeyCode ;
   U_Area.OPS_TPDO.OpsStaW_R=U_Area.DD_OpsDat_R.OpsStaW;
  }
 else
   U_Area.OPS_TPDO.KeyCode_R=0 ;
 U_Area.OPS_TPDO.OpsStaW_R&=0x7fff ;
 if(OpCtrl&CB_OPS_ONLINE_R)
   U_Area.OPS_TPDO.OpsStaW_R|=0x8000 ;
 
//------------------------------------------------------------------- 
 U_Area.RF_TPDO.KeyCode_L=U_Area.PaDat_L.KeyCode ;
 U_Area.RF_TPDO.KeyCode_R=U_Area.PaDat_R.KeyCode ;
 //--
 U_Area.RF_TPDO.PanSta_L=0x00 ;
 if(U_Area.PaDat_L.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_L|=0x80 ;//Battery alarm
 if(U_Area.RSSI_L>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_L|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_L.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_L|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_L) U_Area.RF_TPDO.PanSta_L|=0x10 ;//Transsmiter online flag
 //--
 U_Area.RF_TPDO.PanSta_R=0x00 ;
 if(U_Area.PaDat_R.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_R|=0x80 ;//Battery alarm
 if(U_Area.RSSI_R>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_R|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_R.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_R|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_R) U_Area.RF_TPDO.PanSta_R|=0x10 ;//Transsmiter online flag
 //
 U_Area.RF_TPDO.RSSI_L=(s8) U_Area.RSSI_L ;
 U_Area.RF_TPDO.RSSI_R=(s8) U_Area.RSSI_R ;
//--------------------------------

#ifdef  MA_TEST_SAMPLE
 U_Area.KeyComb =U_Area.RF_TPDO.KeyCode_L|U_Area.RF_TPDO.KeyCode_R ;
 U_Area.KeyComb |=U_Area.OPS_TPDO.KeyCode_L ;
 U_Area.KeyComb |=U_Area.OPS_TPDO.KeyCode_R ;
#endif 

 t_key32=(uint32_t)U_Area.RF_TPDO.KeyCode_R|(uint32_t)U_Area.OPS_TPDO.KeyCode_R;
 t_key32<<=16 ;
 t_key32+=(uint32_t)U_Area.RF_TPDO.KeyCode_L|(uint32_t)U_Area.OPS_TPDO.KeyCode_L ;
 U_Area.t_word32=t_key32; 
 if(t_key32&APP_PB.EStopCode) //
 //if(U_Area.KeyComb&APP_PB.EStopCode) //
 {
   S_Area.EStop_Timer=APP_PB.EStopKeepTime ;//This enable EStop signal to enduring at least 2s
  }
 if(APP_PB.EStopKeepTime)//Keep time valid
 {//enable operation keep delay
   if(S_Area.EStop_Timer)
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
 else
 {//No opeartion keep delay
   //if(U_Area.KeyComb&APP_PB.EStopCode) //
   if(t_key32&APP_PB.EStopCode) //
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
}

void  CANOpenRxTestProc01(void)
{
//--------------------------------
 uint32_t t_key32 ;	
//--------------------------------
//--- U_Area.KeyComb =U_Area.TPDO_RxRF.KeyCode_L|U_Area.TPDO_RxRF.KeyCode_R ;
 t_key32=(uint32_t)U_Area.TPDO_RxRF.KeyCode_R;
 t_key32<<=16 ;
 t_key32+=(uint32_t)U_Area.TPDO_RxRF.KeyCode_L ;	
 U_Area.t_word32=t_key32; 
 if(t_key32&APP_PB.EStopCode) //
// U_Area.KeyComb =U_Area.TPDO_RxRF.KeyCode_L|U_Area.TPDO_RxRF.KeyCode_R ;
// if(U_Area.KeyComb&APP_PB.EStopCode) //
 {
   S_Area.EStop_Timer=APP_PB.EStopKeepTime ;//This enable EStop signal to enduring at least 2s
  }
 if(APP_PB.EStopKeepTime)//Keep time valid
 {//enable operation keep delay
   if(S_Area.EStop_Timer)
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
 else
 {//No opeartion keep delay
   if(t_key32&APP_PB.EStopCode) //
   //if(U_Area.KeyComb&APP_PB.EStopCode) //
     K1_OpEnable();//Operate DO1 ->ERO relay
   else
     K1_OpCancel();//Cancel the Operate DO1 ->ERO relay
  }
	
}
//---------------------
void Proc10mS_JF01(void) //Timed process at frequency=100Hz
{
  U_Area.Counter10ms++ ;
  if(S_Area.EStop_Timer)
    S_Area.EStop_Timer--;
  if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
  {  
    Dat2HostProc_01() ;//
    Canopen2DisplayBuf() ;
   }
}

void Proc100mS_JF01(void)
{
  CANErrMonitor();
}

void Proc1S_JF01(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_JF01(void) //Timed process at interval 1 hour
{
  ;
}


