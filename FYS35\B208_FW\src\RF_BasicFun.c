#include  "SysBasic.h"
#include  "RF_BasicFun.h"

//----------------------------------------------------------------------------------------
timer_t  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

timer_t  RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

__IO uint16_t  RandomS,PRandom ;

volatile uint16_t  RFChipOscSta  ;

//---------------------------------------------
IC021RxSwitchReg   *Rx021_Ch_p_L ;//A pointer to Left side Rx work channel register of IC021
IC021TxSwitchReg   *Tx021_Ch_p_L ;//A pointer to Left side Tx work channel register of IC021
IC021RxSwitchReg   *Rx021_Ch_p_R ;//A pointer to Right side Rx work channel register of IC021
IC021TxSwitchReg   *Tx021_Ch_p_R ;//A pointer to Right side Tx work channel register of IC021

JF01SwitchReg       *Rx01_Ch_p_L ;//A pointer to Left side Rx work channel register of JF01
JF01SwitchReg       *Tx01_Ch_p_L ;//A pointer to Left side Tx work channel register of JF01
JF01SwitchReg       *Rx01_Ch_p_R ;//A pointer to Right side Rx work channel register of JF01
JF01SwitchReg       *Tx01_Ch_p_R ;//A pointer to Right side Tx work channel register of JF01

uint8_t   *RfDat_rp_L  ;//pointer used for Left channel RF receiveing  data
uint8_t   *RfDat_tp_L  ;//pointer used for Left channel RF Transmitte data 
uint8_t   *RfDat_rp_R  ;//pointer used for Right channel RF receiveing  data
uint8_t   *RfDat_tp_R  ;//pointer used for Right channel RF Transmitte data 

uint8_t   *test_p_L ;//pointer used to store current test output poistion of RX data

uint8_t   *test_p_R ;//pointer used to store current testoutput poistion of RX data

volatile  uint16_t  RSSI_STA_L,RSSI_STA_R,RSSI_BufIndex_L,RSSI_BufIndex_R;


RFC_WorkDS_t   RfDs ;//Main data struct used for RF related function

volatile uint8_t   ShiftReg_L ;
volatile uint8_t   BitCounter_L;
volatile uint8_t   PreambleCount_L;
volatile uint8_t   PreambleError_L;
volatile uint8_t   ByteCounter_L;
volatile uint8_t   ShiftReg_R ;
volatile uint8_t   BitCounter_R;
volatile uint8_t   PreambleCount_R;
volatile uint8_t   PreambleError_R;
volatile uint8_t   ByteCounter_R;
LongWord  IC021Reg ;

//RSSI Gain correction table for IC021
const uint8_t gain_correction[] =
    { 86, 0, 0, 0, 58, 38, 24, 0,
	   0, 0, 0, 0, 0, 0, 0, 0
     }; //For AGC and LAN 021

//----
uint8_t  DefPATable[PA_TAB_LEN]={//@315MHz
  0xc2 , //10dBm ,26.9mA
  0xcb , //7dBm  ,22.1mA
  0x85 , //5dBm  ,18.3mA
  0x51 , //0dBm  ,15.0mA
  0x0d , //-20dBm ,11.4mA @315MHz
//  0x69 , //-5dBm ,12.8mA
//  0x34 , //-10dBm ,13.5mA
//  0x1c , //-15dBm ,12.0mA
//  0x0d , //-20dBm ,11.4mA @315MHz
  } ;

//-----------
void InitRfcTimer10(void)
{
  uint16_t i ;
  for(i=0;i<RFC_TIMER10_NUM ;i++)
  {
    RfcTimer10[i].pv=RfcTimer10[i].cv=RfcTimer10[i].csr.csword=0 ;
    }
}

void InitRfcTimer(void)
{
  RfcTimer[0].pv=RfcTimer[0].cv=RfcTimer[0].csr.csword=0 ;
  RfcTimer[1].pv=RfcTimer[1].cv=RfcTimer[1].csr.csword=0 ;
}

void  RfcTimer10Fun(void)
{
  uint16_t i ;
  for(i=0;i<RFC_TIMER10_NUM;i++)
   {
     if(RfcTimer10[i].csr.csbit.en==1)
     {
       if(RfcTimer10[i].cv<65535) ++(RfcTimer10[i].cv) ;
       if(RfcTimer10[i].cv>=RfcTimer10[i].pv)
       {
         RfcTimer10[i].csr.csbit.q=1 ;
        }
      }
   }
}

void RfcTimerFun(void) //This function must be called in system tick process ISR
{
  if(RfcTimer[0].csr.csbit.en==1)
  {
    if(RfcTimer[0].cv<65535) ++(RfcTimer[0].cv) ;
    if(RfcTimer[0].cv>=RfcTimer[0].pv)
    {
      RfcTimer[0].csr.csbit.q=1 ;
     }
   }
  if(RfcTimer[1].csr.csbit.en==1)
  {
    if(RfcTimer[1].cv<65535) ++(RfcTimer[1].cv) ;
    if(RfcTimer[1].cv>=RfcTimer[1].pv)
    {
      RfcTimer[1].csr.csbit.q=1 ;
     }
   }
}
//--------------------------------------------------------------
void WaitSomeTime(uint16_t delay)
{//Do not change for time critical--@ HCLK=64MHz
  uint16_t tmp ;
  for(tmp=0 ;tmp <delay ;tmp++)
  {
   RandomS +=(SysTick->VAL<<5) ;
   RandomS +=(SysTick->VAL<<7) ;
  }
  PRandom &=(~ENC_MASK) ;
  PRandom |=(RandomS&ENC_MASK) ;
}
//--------------------------------------------------------------

uint16_t CRC16WithSeed (uint16_t seed,const uint16_t *nData, uint16_t wLength)//Gerate 16 bit CRC value from series words
{
  uint16_t nTemp  ;//byte value (data saved as 16 bit word)
  uint16_t wCRCWord ;
  wCRCWord =seed;
  while (wLength--)
  {
     nTemp     = (*nData >>8) ;
     nTemp     ^= wCRCWord ;
     nTemp     &= 0x00ff ;
     wCRCWord  >>= 8 ;
     wCRCWord  &=0x00ff ; //A right shift CCS always do arithmetic shift
     wCRCWord  ^=wCRCTable[nTemp];// PFUNC_wordRead(TBp) ;

     nTemp      = *nData ^ wCRCWord;
     nData++ ;
     nTemp    &=0x00ff ;
     wCRCWord >>= 8;
     wCRCWord &= 0x00ff ; //A right shift CCS always do arithmetic shift
     wCRCWord ^= wCRCTable[nTemp];//PFUNC_wordRead(TBp) ;
  }
  nTemp=wCRCWord ;
  wCRCWord>>=8 ;
  wCRCWord &=0x00ff ;
  nTemp<<=8 ;
  wCRCWord |=nTemp ;
  return wCRCWord;  //Pattention to CRC word's Lo and Hi byte order*********
}
//---------
void  InitRfEnvironment(void)
{//Setup proper run condition(varible init) for RF communication function
  ClrObj16((uint16_t *)&RfDs,sizeof(RFC_WorkDS_t)>>1);
  RfDs.PAccStaS.UIDCode=CalReqCode() ;//Get Authentication request code for this module
  EXTI->PR   =0x0000018c ;//clear current pending interrupt flag for left and right side DIO(GDO2),DCLK(GDO0)
  EXTI->IMR &=0xfffffe73 ;//disable (GDO2(DIO),GDO0(DCLK)) signal to generate interrupt
  if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS35_021_SHR_MODE)
  {//This device be configured to work in FYF35 shearer mode
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS35_021_TUN_MODE)
  {//This device be configured to work in FYF35 tunneller mode
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS35_01_BI_MODE)
  {
	;
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS35_01_UNI_MODE)
  {//This device be configured to work in FYF25 receiver mode
    ;
   }
  else
  {//I Assume must ==JF01_IAP_MODE
    ;
   }
  RfDs.TxPackageLen = sizeof(SHR_TX_DAT) ;
#ifdef USE_OLD_FRAME
  RfDs.RxPackageLen = sizeof(PANEL_OLD_TX_DAT) ;
#else   
  RfDs.RxPackageLen = sizeof(PANEL_TX_DAT) ;
#endif   
//---------------------------
  RFChipOscSta=RFIC_OSC_UNKNOW ;//The RFChipOscSta must stay in RFIC_OSC_READY status for right side operation,
  RfDs.JF01Init_L.OpSta=NOT_INIT_STA ;
  RfDs.JF01Init_R.OpSta=NOT_INIT_STA ;

//---------------------------
  Rx021_Ch_p_L=(IC021RxSwitchReg *) &(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
  Tx021_Ch_p_L=(IC021TxSwitchReg *) &(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
  Rx021_Ch_p_R=(IC021RxSwitchReg *) &(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
  Tx021_Ch_p_R=(IC021TxSwitchReg *) &(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;

  Rx01_Ch_p_L=(JF01SwitchReg *) &(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
  Tx01_Ch_p_L=(JF01SwitchReg *) &(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
  Rx01_Ch_p_R=(JF01SwitchReg *) &(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
  Tx01_Ch_p_R=(JF01SwitchReg *) &(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;

  test_p_L=(uint8_t *) U_Area.Ultra_BUF_L ;
  test_p_R=(uint8_t *) U_Area.Ultra_BUF_R ;
//------------------------
  RSSI_STA_L=0x0002 ;
  RSSI_STA_R=0x0002 ;
  RSSI_BufIndex_L=0 ;
  RSSI_BufIndex_R=0 ;
 //-----------------------
  RfcTimer[RF_TIMER_L].pv=APP_PA.JF01_RxAttenMTO ;//--55 mS,JF01 Rx signal anttenuation setting restore monitor,IC021 routine will reinit the default value
  RfcTimer[RF_TIMER_L].cv=0 ;
  RfcTimer[RF_TIMER_L].csr.csword=0x0000 ; //disable RfcTimer[0]
  RfcTimer[RF_TIMER_R].pv=APP_PA.JF01_RxAttenMTO ;//--55 mS,JF01 Rx signal anttenuation setting restore monitor,IC021 routine will reinit the default value
  RfcTimer[RF_TIMER_R].cv=0 ;
  RfcTimer[RF_TIMER_R].csr.csword=0x0000 ; //disable RfcTimer[1]

  RfcTimer10[RF_OP_TIMER_L].pv=APP_PA.OpcKeepTime ;
  RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[RF_OP_TIMER_L].cv=0 ;
  RfcTimer10[RF_OP_TIMER_R].pv=APP_PA.OpcKeepTime ;
  RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[RF_OP_TIMER_R].cv=0 ;

  RfcTimer10[AIR_TIMER_L].pv=APP_PA.AirLinkageTO ;
  RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[AIR_TIMER_L].cv=0 ;
  RfcTimer10[AIR_TIMER_R].pv=APP_PA.AirLinkageTO ;
  RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[AIR_TIMER_R].cv=0 ;

//-----------------------
  RfDs.AreaFlag024=0xf024 ;
  RfDs.AreaFlag100=0xf100 ;
  RfDs.DClkCntChk_L=480 ; //Lead the first 100ms RFIC status check passed automaticlly
  RfDs.DClkCntChk_R=480 ;
}

//Init SPI1 interface to access IC021 @8Mbps ,16 bits character,use poll mode
void InitSPI1_021(void)
{
 /* Enable SPI1  clocks */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1 , ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , ENABLE);  /* Reset SPI1 First */
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , DISABLE); /*SPI1 Out of reset  status */
 /*SPI interface to access RF chip @3.6846Mbps ,8 bits character,use poll mode*/
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=0xb8b00000 ;//set SCLK,MOSI to AF OUT mode,MISO to Input mode
  SPI1->CR1 = 0x0b0c ;//0x0b0c nSS internal mode 1,Master Mode,16 bit data length, disabled,CPHA=0,CPOL=0
                      //14.7456 MHz APB2 clock(14.7456MHz CPU)/4=3.6864 Mbps(0x0b14)
  SPI1->CR2  =0x0000 ;//No interrupt,No DMA
  SPI1->I2SCFGR =0x0000 ;//Use SPI mode only
 /* Enable SPI1 */
  SPI1->CR1 |=0x0040 ;
}
//-------------------------
void InitSPI1_01(void)
{
 /* Enable SPI1  clocks */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1 , ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , ENABLE);  /* Reset SPI1 First */
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , DISABLE); /*SPI1 Out of reset  status */
 /*SPI interface to access RF chip @3.6846Mbps ,8 bits character,use poll mode*/
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=0xb8b00000 ;//set SCLK,MOSI to AF OUT mode,MISO to Input mode
  SPI1->CR1 = 0x030c ;//0x0304 nSS internal mode 1,Master Mode,8 bit data length, disabled,CPHA=0,CPOL=0
                      //14.7456MHz APB2 clock/4=3.6864Mbps
  SPI1->CR2  =0x0000 ;//No interrupt,No DMA
  SPI1->I2SCFGR =0x0000 ;//Use SPI mode only
 /* Enable SPI1 */
  SPI1->CR1 |=0x0040 ;
}
//---------------------------------
void InitRfTickTimer(void )
{ //Timer2 be set to generate required periodical interrupt--as RF operation ticks
  TIM2->SR=0xffe0 ;//Clear the inperrupt flag
  TIM2->CR1=0x0284 ;
  TIM2->CNT=0x0000 ;//
  TIM2->PSC=731 ;//TIM2 prescaler register,24.000MHz/32.768kHz=732.4218
  TIM2->ARR=APP_PA.RfTickTimerPV  ;//TIM2 auto-reload register ,for Fyf35 use 3275(10.005Hz),for Fyf25 use 1536(21.33Hz)
  TIM2->DIER=0x0001 ;//TIM2 DMA/interrupt disable register ,only allow update interrupt
  TIM2->CR1=0x0285 ;//digital filter sample clock=4x tck_int,Up count mode ,only counter overflow gen interrupt,enable the counter
  TIM2->EGR=0x0001 ;//Manual set a update event force update PSC register
 }

void InitRfTickTimer_HSI(void)
{
  TIM2->SR=0xffe0 ;//Clear the inperrupt flag
  TIM2->CR1=0x0284 ;
  TIM2->CNT=0x0000 ;//
  TIM2->PSC=243 ;//TIM2 prescaler register,(8.0000MHz HCLK) /32.768kHz=244.14
  TIM2->ARR=APP_PA.RfTickTimerPV  ;//TIM2 auto-reload register,for Fyf35 use 3275(10.005Hz),for Fyf25 use 1536(21.33Hz)
  TIM2->DIER=0x0001 ;//TIM2 DMA/interrupt disable register ,only allow update interrupt
  TIM2->CR1=0x0285 ;//digital filter sample clock=4x tck_int,Up count mode ,only counter overflow gen interrupt,enable the counter
  TIM2->EGR=0x0001 ;//Manual set a update event force update PSC register
 }

//----------------------------------------------------------------------

void ShrMachDatTxLoad_021(void)
{
 U_Area.ShrMachDat_TB0.WcrStaW=0x0000 ;
 // set air linkage valid flag
#ifdef  USE_OLD_FRAME
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.ShrMachDat_TB0.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.ShrMachDat_TB0.RightLinkOk=1;
  }
#else	
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.PSta.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.ShrMachDat_TB0.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.PSta.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.ShrMachDat_TB0.RightLinkOk=1;
  }
#endif
 //--------------
 if(U_Area.RSSI_L>=RSSI_LIMIT) U_Area.ShrMachDat_TB0.RSSI_Ok_L=1 ;
 if(U_Area.RSSI_R>=RSSI_LIMIT) U_Area.ShrMachDat_TB0.RSSI_Ok_R=1 ;
 //----------------
 if((OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))||(U_Area.WiredComSta&0x0010))//Rx station module communicate with HOST(CAN bus)or Demo module OK
   U_Area.ShrMachDat_TB0.DatValid=1 ;
 //----------------
 if(RfDs.RfPollNum&0x0001)
   U_Area.ShrMachDat_TB0.ToggleBit=1 ;//Set toggle bit to guide correct channel use
 //----------------
 if(U_Area.DatReq_CP)//machine Rx station will output request for right side
 {
   U_Area.DatReq_CP=0 ;
   if(OpCtrl&CB_T_ONLINE_R)   U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
   else  U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
 }
 else
 {
   U_Area.DatReq_CP=1 ;
   if(OpCtrl&CB_T_ONLINE_L)  U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
   else  U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
 }
 U_Area.ShrMachDat_TB0.WcrStaW |= U_Area.DatGID_C&0x001f;//Set replayed display data GID0
 U_Area.ShrMachDat_TB0.WcrStaW |= (U_Area.DatGID_C>>3)&0x03e0;//Set replayed display data GID1
 U_Area.DisplayDatBuf[26]=U_Area.RSSI_L ;//for debug test
 U_Area.DisplayDatBuf[27]=U_Area.RSSI_R ;//for debug test
 U_Area.ShrMachDat_TB0.MachSta  = U_Area.DisplayDatBuf[0] ;//For machine status data
 U_Area.ShrMachDat_TB0.Dat_1 =U_Area.DisplayDatBuf[U_Area.DatGID_C&0x001f] ;//--debug
 U_Area.ShrMachDat_TB0.Dat_2 =U_Area.DisplayDatBuf[((U_Area.DatGID_C>>8)&0x001f)] ;//--debug
 //---------
 U_Area.ShrMachDat_TB0.CRC_app = CRC16WithSeed(MACHINE_CRC_SEED,(const uint16_t *)U_Area.RfTxBuf0,(sizeof(SHR_TX_DAT)/2-1));//Tx data load fixed to 4 words
}
//-------------------------------------
void ShrMachDatTxLoad_01(void)
{
 U_Area.ShrMachDat.WcrStaW=0x0000 ;
 // set air linkage valid flag
#ifdef  USE_OLD_FRAME
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.ShrMachDat.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.ShrMachDat.RightLinkOk=1;
  }
#else	
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.PSta.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.ShrMachDat.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.PSta.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.ShrMachDat.RightLinkOk=1;
  }
#endif  
 //--------------
 if(U_Area.RSSI_L>=RSSI_LIMIT) U_Area.ShrMachDat.RSSI_Ok_L=1 ;
 if(U_Area.RSSI_R>=RSSI_LIMIT) U_Area.ShrMachDat.RSSI_Ok_R=1 ;
 //----------------
 if(OpCtrl &(CB_MDM_COM_OK+CB_MDS_COM_OK))//Rx station module communicate with HOST(CAN bus)or Demo module OK
   U_Area.ShrMachDat.DatValid=1 ;
 //----------------
 if(RfDs.RfPollNum&0x0001)
   U_Area.ShrMachDat.ToggleBit=1 ;//Set toggle bit to guide correct channel use
 //----------------
 if(U_Area.DatReq_CP)//machine Rx station will output request for right side
 {
   U_Area.DatReq_CP=0 ;
   if(OpCtrl&CB_T_ONLINE_R) U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
   else	 U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
  }
 else
 {
   U_Area.DatReq_CP=1 ;
   if(OpCtrl&CB_T_ONLINE_L) U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
   else U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
 }
 U_Area.ShrMachDat.WcrStaW |= U_Area.DatGID_C&0x001f;//Set replayed display data GID0
 U_Area.ShrMachDat.WcrStaW |= (U_Area.DatGID_C>>3)&0x03e0;//Set replayed display data GID1

 U_Area.DisplayDatBuf[26]=U_Area.RSSI_L ;
 U_Area.DisplayDatBuf[27]=U_Area.RSSI_R ;
 U_Area.ShrMachDat.MachSta  = U_Area.DisplayDatBuf[0] ;//For machine status data

 U_Area.ShrMachDat.Dat_1 = U_Area.DisplayDatBuf[U_Area.DatGID_C&0x001f] ;
 U_Area.ShrMachDat.Dat_2 = U_Area.DisplayDatBuf[((U_Area.DatGID_C>>8)&0x001f)] ;
 //---------
 U_Area.ShrMachDat.CRC_app = CRC16WithSeed(MACHINE_CRC_SEED,(const uint16_t *)U_Area.RfDatAsm,(sizeof(SHR_TX_DAT)/2-1));//Tx data load fixed to 4 words
 U_Area.ShrMachDat_TB0.WcrStaW =U_Area.ShrMachDat.WcrStaW^0xaaaa ;
 U_Area.ShrMachDat_TB0.MachSta =U_Area.ShrMachDat.MachSta^0xaaaa ;
 U_Area.ShrMachDat_TB0.Dat_1 =U_Area.ShrMachDat.Dat_1^0xaaaa ;
 U_Area.ShrMachDat_TB0.Dat_2 =U_Area.ShrMachDat.Dat_2^0xaaaa ;
 U_Area.ShrMachDat_TB0.CRC_app =U_Area.ShrMachDat.CRC_app^0xaaaa ;
}
//--------------------------------------
void  RSSI_Filter_021(void)
{
 int16_t i,acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :
      break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq() ;
	  RfDs.RSSI_D_L=Read_RSSI_021_L() ;
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=RfDs.RSSI_D_L;
      if(RSSI_BufIndex_L>=4)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
    RfDs.RSSI_L=(acc>>2) ;
    RfDs.RSSI_L+=APP_PA.RSSIOffset_L ;
	if(RfDs.RSSI_L<-128) RfDs.RSSI_L=-128 ; 
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
 switch(RSSI_STA_R)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_R)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_R ;
      __enable_irq() ;
	  RfDs.RSSI_D_R=Read_RSSI_021_R() ;
      RfDs.RSSI_BUF_R[RSSI_BufIndex_R++]=RfDs.RSSI_D_R;
     if(RSSI_BufIndex_R>=4)
     {
      RSSI_BufIndex_R=0 ;
      RSSI_STA_R=0x0004 ;
      }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_R[i] ;
     }
    RfDs.RSSI_R=(acc>>2) ;
    RfDs.RSSI_R+=APP_PA.RSSIOffset_R ;
	if(RfDs.RSSI_R<-128) RfDs.RSSI_R=-128 ; 
    RSSI_STA_R=0x0002 ;
    break ;
  default :
    RSSI_STA_R=0x0000 ;
 }
}

//-----------------------------------
void  RSSI_Filter_01(void)
{
 int16_t  i, acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :

      break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq() ;
	  RfDs.RSSI_D_L=Read_RSSI_01_L() ;
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=RfDs.RSSI_D_L;
      if(RSSI_BufIndex_L>=4)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
     RfDs.RSSI_L=(acc>>2) ;
     RfDs.RSSI_L+=APP_PA.RSSIOffset_L ;
    if( RfDs.RSSI_L<-128)  RfDs.RSSI_L=-128 ;
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
 switch(RSSI_STA_R)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_R)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_R ;
      __enable_irq() ;
	  RfDs.RSSI_D_R=Read_RSSI_01_R() ;
      RfDs.RSSI_BUF_R[RSSI_BufIndex_R++]=RfDs.RSSI_D_R;
      if(RSSI_BufIndex_R>=4)
      {
        RSSI_BufIndex_R=0 ;
        RSSI_STA_R=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_R[i] ;
     }
     RfDs.RSSI_R=(acc>>2) ;
     RfDs.RSSI_R+=APP_PA.RSSIOffset_R ;
    if( RfDs.RSSI_R<-128)  RfDs.RSSI_R=-128 ;
    RSSI_STA_R=0x0002 ;
    break ;
  default :
    RSSI_STA_R=0x0000 ;
 }
}
//-------------------------------------------------------
void  RfSetupTestEntry(__IO uint8_t *RDp)
{//only allow three wrods command--this function direct import from MSP430 program and may not work in ARM system
  TestKeyA=(uint16_t)(*RDp++) ;
  TestKeyA<<=8 ;
  TestKeyA|=(uint16_t)*RDp++ ;//get the hole word

  TestKeyB=(uint16_t)(*RDp++) ;
  TestKeyB<<=8 ;
  TestKeyB|=(uint16_t)*RDp++ ;//get the hole word

  TestCtrlW=(uint16_t)(*RDp++) ;
  TestCtrlW<<=8 ;
  TestCtrlW|=(uint16_t)*RDp++ ;//get the hole word
  if((TestKeyA==TEST_KEY_ID_A)&&(TestKeyB==TEST_KEY_ID_B))
  {
    SCB->AIRCR = 0x05fa0001;//do (system &) core reset,see Cortex-M3 Technical Reference Manaul page 8_23
   }
} 

//-----------------
